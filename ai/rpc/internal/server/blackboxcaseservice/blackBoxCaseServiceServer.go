// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: ai.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	blackboxcaseservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcaseservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BlackBoxCaseServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedBlackBoxCaseServiceServer
}

func NewBlackBoxCaseServiceServer(svcCtx *svc.ServiceContext) *BlackBoxCaseServiceServer {
	return &BlackBoxCaseServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateBlackBoxCase create a black box case
func (s *BlackBoxCaseServiceServer) CreateBlackBoxCase(ctx context.Context, in *pb.CreateBlackBoxCaseReq) (*pb.CreateBlackBoxCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseservicelogic.NewCreateBlackBoxCaseLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCase(in)
}

// DeleteBlackBoxCase create a black box case
func (s *BlackBoxCaseServiceServer) DeleteBlackBoxCase(ctx context.Context, in *pb.DeleteBlackBoxCaseReq) (*pb.DeleteBlackBoxCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseservicelogic.NewDeleteBlackBoxCaseLogic(ctx, s.svcCtx)

	return l.DeleteBlackBoxCase(in)
}

// UpdateBlackBoxCase create a black box case
func (s *BlackBoxCaseServiceServer) UpdateBlackBoxCase(ctx context.Context, in *pb.UpdateBlackBoxCaseReq) (*pb.UpdateBlackBoxCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseservicelogic.NewUpdateBlackBoxCaseLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCase(in)
}

// UpdateBlackBoxCase create a black box case
func (s *BlackBoxCaseServiceServer) UpdateBlackBoxCaseAI(ctx context.Context, in *pb.UpdateBlackBoxCaseAIReq) (*pb.UpdateBlackBoxCaseAIResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseservicelogic.NewUpdateBlackBoxCaseAILogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseAI(in)
}

// GetBlackBoxCase gets a black box case
func (s *BlackBoxCaseServiceServer) GetBlackBoxCase(ctx context.Context, in *pb.GetBlackBoxCaseReq) (*pb.GetBlackBoxCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseservicelogic.NewGetBlackBoxCaseLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCase(in)
}

// ListBlackBoxCase list black box case
func (s *BlackBoxCaseServiceServer) ListBlackBoxCase(ctx context.Context, in *pb.ListBlackBoxCaseReq) (*pb.ListBlackBoxCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseservicelogic.NewListBlackBoxCaseLogic(ctx, s.svcCtx)

	return l.ListBlackBoxCase(in)
}

// MergeBlackBoxCaseData list black box case
func (s *BlackBoxCaseServiceServer) MergeBlackBoxCaseData(ctx context.Context, in *pb.MergeBlackBoxCaseDataReq) (*pb.MergeBlackBoxCaseDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseservicelogic.NewMergeBlackBoxCaseDataLogic(ctx, s.svcCtx)

	return l.MergeBlackBoxCaseData(in)
}

// QueryIncBlackBoxCase list black box case
func (s *BlackBoxCaseServiceServer) QueryIncBlackBoxCase(ctx context.Context, in *pb.QueryIncBlackBoxCaseReq) (*pb.QueryIncBlackBoxCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseservicelogic.NewQueryIncBlackBoxCaseLogic(ctx, s.svcCtx)

	return l.QueryIncBlackBoxCase(in)
}
