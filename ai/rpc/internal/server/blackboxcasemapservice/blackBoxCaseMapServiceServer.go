// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: ai.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	blackboxcasemapservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcasemapservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BlackBoxCaseMapServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedBlackBoxCaseMapServiceServer
}

func NewBlackBoxCaseMapServiceServer(svcCtx *svc.ServiceContext) *BlackBoxCaseMapServiceServer {
	return &BlackBoxCaseMapServiceServer{
		svcCtx: svcCtx,
	}
}

// GetBlackBoxCaseMap get blackbox case map
func (s *BlackBoxCaseMapServiceServer) GetBlackBoxCaseMap(ctx context.Context, in *pb.GetBlackBoxCaseMapReq) (*pb.GetBlackBoxCaseMapResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasemapservicelogic.NewGetBlackBoxCaseMapLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseMap(in)
}

// UpdateBlackBoxCaseMap update blackbox case map
func (s *BlackBoxCaseMapServiceServer) UpdateBlackBoxCaseMap(ctx context.Context, in *pb.UpdateBlackBoxCaseMapReq) (*pb.UpdateBlackBoxCaseMapResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasemapservicelogic.NewUpdateBlackBoxCaseMapLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseMap(in)
}

// CreateBlackBoxCaseMap create blackbox case map
func (s *BlackBoxCaseMapServiceServer) CreateBlackBoxCaseMap(ctx context.Context, in *pb.CreateBlackBoxCaseMapReq) (*pb.CreateBlackBoxCaseMapResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasemapservicelogic.NewCreateBlackBoxCaseMapLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseMap(in)
}

// GetCompleteBlackBoxCaseMap get complete blackbox case map
func (s *BlackBoxCaseMapServiceServer) GetCompleteBlackBoxCaseMap(ctx context.Context, in *pb.GetCompleteBlackBoxCaseMapReq) (*pb.GetCompleteBlackBoxCaseMapResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasemapservicelogic.NewGetCompleteBlackBoxCaseMapLogic(ctx, s.svcCtx)

	return l.GetCompleteBlackBoxCaseMap(in)
}

// CreateBlackBoxCaseMapId create blackbox case map_id
func (s *BlackBoxCaseMapServiceServer) CreateBlackBoxCaseMapId(ctx context.Context, in *pb.CreateBlackBoxCaseMapIdReq) (*pb.CreateBlackBoxCaseMapIdResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasemapservicelogic.NewCreateBlackBoxCaseMapIdLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseMapId(in)
}
