package httpc

import (
	"bufio"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type ResponseWithErrors struct {
	Err  error
	Stop error
}

// Error implements error.
func (r ResponseWithErrors) Error() string {
	panic("unimplemented")
}

func CreateKnowledge(baseUrl string, in CreateKnowledgeReq) (ResponseData[*pb.BlackBoxCaseKnowledge], error) {
	var respData ResponseData[*pb.BlackBoxCaseKnowledge]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/v2/test-case/knowledge/create", in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func ListKnowledge(baseUrl string, ids []string) (ResponseData[[]*ListKnowledgeResp], error) {
	params := map[string]string{
		"knowledge_ids": strings.Join(ids, ","),
	}
	var respData ResponseData[[]*ListKnowledgeResp]
	err := ExecuteRequest(
		http.MethodGet, baseUrl, MapToQueryString(params, "/api/v2/test-case/knowledge/list"), nil, &respData,
	)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func GetKnowledge(baseUrl string, ids []string) (ResponseData[[]*ListKnowledgeResp], error) {
	params := map[string]string{
		"knowledge_ids": strings.Join(ids, ","),
	}
	var res ResponseData[[]*ListKnowledgeResp]
	err := ExecuteRequest(
		http.MethodGet, baseUrl, MapToQueryString(params, "/api/v2/test-case/knowledge/list"), nil, &res,
	)
	if err != nil {
		return res, err
	}
	return res, nil
}

func GetKnowledgeTitle(baseUrl, id string) (ResponseData[any], error) {
	params := map[string]string{
		"knowledge_id": id,
	}
	var res ResponseData[interface{}]
	err := ExecuteRequest(
		http.MethodGet, baseUrl, MapToQueryString(params, "/api/v2/test-case/knowledge/title/list"), nil, &res,
	)
	if err != nil {
		return res, err
	}
	return res, nil
}

func GetCaseJson(baseUrl string, id string) (ResponseData[[]*pb.BlackBoxCaseData], error) {
	params := map[string]string{
		"case_id": strings.Join([]string{id}, ","),
	}
	var respData ResponseData[[]*pb.BlackBoxCaseData]
	err := ExecuteRequest(http.MethodGet, baseUrl, MapToQueryString(params, "/api/v2/test-case/get"), nil, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}

	return respData, nil
}

func GetCaseRowCount(baseUrl string, id string) (ResponseData[int64], error) {
	params := map[string]string{
		"case_id": strings.Join([]string{id}, ","),
	}
	var respData ResponseData[int64]
	err := ExecuteRequest(
		http.MethodGet, baseUrl, MapToQueryString(params, "/api/v2/test-case/statistic/row-count/get"), nil, &respData,
	)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}

	return respData, nil
}

func Json2MD(baseUrl string, in CaseTableReq) (ResponseData[string], error) {
	var respData ResponseData[string]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/v2/test-case/table/convert", in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}

	return respData, nil
}

func GetAIState(baseUrl string, id string) (ResponseData[GetAIStateResp], error) {
	params := map[string]string{
		"case_id": strings.Join([]string{id}, ","),
	}
	var respData ResponseData[GetAIStateResp]
	err := ExecuteRequest(
		http.MethodGet, baseUrl, MapToQueryString(params, "/api/v2/test-case/ai-state/get"), nil, &respData,
	)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}

	return respData, nil
}

func TransMindGraph(baseUrl string, in CaseTableReq) (ResponseData[string], error) {
	var res ResponseData[string]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/v2/test-case/graph/mind/transfer", in, &res)
	if err != nil {
		return res, err
	}
	if res.Code != 0 {
		return res, errors.New(res.Message)
	}
	return res, nil
}

func TransActivitiesGraph(baseUrl string, in CaseTableReq) (ResponseData[string], error) {
	var res ResponseData[string]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/v2/test-case/graph/activities-mind/transfer", in, &res)
	if err != nil {
		return res, err
	}
	if res.Code != 0 {
		return res, errors.New(res.Message)
	}
	return res, nil
}

func TransReviewMindGraph(baseUrl string, in CaseTableReq) (ResponseData[string], error) {
	var res ResponseData[string]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/v2/test-case/graph/review-mind/transfer", in, &res)
	if err != nil {
		return res, err
	}
	if res.Code != 0 {
		return res, errors.New(res.Message)
	}
	return res, nil
}

func MergeCases(baseUrl string, in MergeCasesReq) (ResponseData[[]*pb.BlackBoxCaseData], error) {
	var res ResponseData[[]*pb.BlackBoxCaseData]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/v2/test-case/table/merge", in, &res)
	if err != nil {
		return res, err
	}
	if res.Code != 0 {
		return res, errors.New(res.Message)
	}
	return res, nil
}

func DeleteKnowledge(baseUrl string, id string) error {
	params := map[string]string{
		"knowledge_id": id,
	}
	var respData ResponseData[struct{}]
	err := ExecuteRequest(
		http.MethodDelete, baseUrl, MapToQueryString(params, "/api/v2/test-case/knowledge/delete"), nil, &respData,
	)
	if err != nil {
		return err
	}
	if respData.Code != 0 {
		return errors.New(respData.Message)
	}
	return nil
}

func ReloadKnowledge(baseUrl string, id string) error {
	var respData ResponseData[struct{}]
	err := ExecuteRequest(
		http.MethodPost, baseUrl, "/api/v2/test-case/knowledge/reload", ReloadKnowledgeReq{KnowledgeId: id}, &respData,
	)
	if err != nil {
		return err
	}
	if respData.Code != 0 {
		return errors.New(respData.Message)
	}
	return nil
}

func ReorderCaseData(baseUrl string, in ReorderCaseDataReq) (ResponseData[[]*pb.BlackBoxCaseData], error) {
	var respData ResponseData[[]*pb.BlackBoxCaseData]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/v2/test-case/table/sort", in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func QueryKnowledge(baseUrl string, messageReq *CreateTestCaseDataReq, stream *sse.Stream) error {
	bodyBytes, err := json.Marshal(messageReq)
	if err != nil {
		setStreamError(stream, "参数解析失败")
		return err
	}

	req, err := http.NewRequest("POST", baseUrl+"/api/v2/test-case/query", bytes.NewReader(bodyBytes))
	if err != nil {
		setStreamError(stream, "接口异常初始化，请稍后重试")
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	defer func(r *http.Response) {
		if r != nil {
			_ = r.Body.Close()
		}
	}(resp)
	if err != nil {
		setStreamError(stream, "接口异常，请稍后重试")
		return fmt.Errorf("error sending: %s", err)
	}

	err = resp2Stream(resp, stream, "")
	if err != nil {
		return err
	}
	return nil
}

func CreateTestCaseData(baseUrl string, messageReq *CreateTestCaseDataReq, stream *sse.Stream) error {
	// 提前给到前端case_ref_id,使其能够在ai流生成用例前就能查询其状态
	stream.Event <- &sse.Event{
		ID: []byte(stream.ID),
		Data: jsonx.MarshalIgnoreError(
			&CreateCaseDataResp{
				RevisionId: messageReq.Revision,
				CaseRefId:  messageReq.CaseId,
			},
		),
	}
	bodyBytes, err := json.Marshal(messageReq)
	if err != nil {
		setStreamError(stream, "参数解析失败")
		return err
	}
	println(string(bodyBytes))

	req, err := http.NewRequest("POST", baseUrl+"/api/v2/test-case/create", bytes.NewReader(bodyBytes))
	if err != nil {
		setStreamError(stream, "接口异常初始化，请稍后重试")
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	defer func(r *http.Response) {
		if r != nil {
			_ = r.Body.Close()
		}
	}(resp)
	if err != nil {
		setStreamError(stream, "接口异常，请稍后重试")
		return fmt.Errorf("error sending: %s", err)
	}

	err = resp2Stream(resp, stream, messageReq.Revision)
	if err != nil {
		return err
	}
	return nil
}

func PartialEditTestCaseData(baseUrl string, messageReq *PartialEditTestCaseDataReq, stream *sse.Stream) error {
	// 提前给到前端case_ref_id,使其能够在ai流生成用例前就能查询其状态
	stream.Event <- &sse.Event{
		ID: []byte(stream.ID),
		Data: jsonx.MarshalIgnoreError(
			&CreateCaseDataResp{
				RevisionId: "",
				CaseRefId:  messageReq.CaseId,
			},
		),
	}
	bodyBytes, err := json.Marshal(messageReq)
	if err != nil {
		setStreamError(stream, "参数解析失败")
		return err
	}
	println(string(bodyBytes))

	req, err := http.NewRequest("POST", baseUrl+"/api/v2/test-case/partial-edit", bytes.NewReader(bodyBytes))
	if err != nil {
		setStreamError(stream, "接口异常初始化，请稍后重试")
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	defer func(r *http.Response) {
		if r != nil {
			_ = r.Body.Close()
		}
	}(resp)
	if err != nil {
		setStreamError(stream, "接口异常，请稍后重试")
		return fmt.Errorf("error sending: %s", err)
	}

	err = resp2Stream(resp, stream, messageReq.Revision)
	if err != nil {
		return err
	}
	return nil
}

func EditTestCaseData(baseUrl string, messageReq *EditTestCaseDataReq, stream *sse.Stream) error {
	// 提前给到前端case_ref_id,使其能够在ai流生成用例前就能查询其状态
	stream.Event <- &sse.Event{
		ID: []byte(stream.ID),
		Data: jsonx.MarshalIgnoreError(
			&CreateCaseDataResp{
				RevisionId: messageReq.Revision,
				CaseRefId:  messageReq.CaseId,
			},
		),
	}
	bodyBytes, err := json.Marshal(messageReq)
	if err != nil {
		setStreamError(stream, "参数解析失败")
		return err
	}
	println(string(bodyBytes))

	req, err := http.NewRequest("POST", baseUrl+"/api/v2/test-case/edit", bytes.NewReader(bodyBytes))
	if err != nil {
		setStreamError(stream, "接口异常初始化，请稍后重试")
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	defer func(r *http.Response) {
		if r != nil {
			_ = r.Body.Close()
		}
	}(resp)
	if err != nil {
		setStreamError(stream, "接口异常，请稍后重试")
		return fmt.Errorf("error sending: %s", err)
	}

	err = resp2Stream(resp, stream, messageReq.Revision)
	if err != nil {
		return err
	}
	return nil
}

func GetEditTestCaseDataProgress(baseUrl string, caseRefId string, stream *sse.Stream) (resErr ResponseWithErrors) {
	params := map[string]string{
		"case_id": caseRefId,
	}
	req, err := http.NewRequest(
		"GET", baseUrl+MapToQueryString(params, "/api/v2/test-case/progress-edit/get"), bytes.NewReader([]byte{}),
	)
	if err != nil {
		setStreamError(stream, "接口异常初始化，请稍后重试")
		resErr.Err = err
		return resErr
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	defer func(r *http.Response) {
		if r != nil {
			_ = r.Body.Close()
		}
	}(resp)
	if err != nil {
		setStreamError(stream, "接口异常，请稍后重试")
		resErr.Err = fmt.Errorf("error sending: %s", err)
		return resErr
	}

	if resp.StatusCode != http.StatusOK {
		setStreamError(stream, "接口异常，请稍后重试。"+resp.Status)
		resErr.Err = fmt.Errorf("unexpected status code: %d", resp.StatusCode)
		return resErr
	}

	reader := bufio.NewReader(resp.Body)
	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				resErr.Stop = err
				return resErr
			}
			resErr.Err = err
			return resErr
		}
		if strings.HasPrefix(line, "data:") && strings.HasSuffix(line, "\n") {
			data := strings.TrimPrefix(line[len("data: "):], "\n")
			// 将'转为",否则json识别不了
			// data = strings.ReplaceAll(data, "'", "\"")
			// AI接口的case_id对应着这边的case_ref_id,revison_id从本服务生成
			var progressData ResponseData[int64]
			err = json.Unmarshal([]byte(data), &progressData)
			// resp := CreateCaseDataResp{
			// 	RevisionId: revisionId,
			// 	CaseRefId:  caseData.MapData.CaseId,
			// 	Content:    caseData.MapData.Content,
			// }
			if err != nil {
				println(err.Error())
				break
			}
			stream.Event <- &sse.Event{
				ID:   []byte(stream.ID),
				Data: jsonx.MarshalIgnoreError(&progressData),
			}
		}
	}
	return resErr
}

func resp2Stream(resp *http.Response, stream *sse.Stream, revisionId string) error {
	if resp.StatusCode != http.StatusOK {
		setStreamError(stream, "接口异常，请稍后重试。"+resp.Status)
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	reader := bufio.NewReader(resp.Body)
	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			println("读取流异常", err.Error())
			break
		}
		if strings.HasPrefix(line, "data:") && strings.HasSuffix(line, "\n") {
			data := strings.TrimPrefix(line[len("data: "):], "\n")
			// 将'转为",否则json识别不了
			// data = strings.ReplaceAll(data, "'", "\"")
			// AI接口的case_id对应着这边的case_ref_id,revison_id从本服务生成
			var caseData ResponseData[CreateCaseData]
			err = json.Unmarshal([]byte(data), &caseData)
			resp := CreateCaseDataResp{
				RevisionId: revisionId,
				CaseRefId:  caseData.Data.CaseId,
				Content:    caseData.Data.Content,
			}
			if err != nil {
				println(err.Error())
				break
			}
			stream.Event <- &sse.Event{
				ID:   []byte(stream.ID),
				Data: jsonx.MarshalIgnoreError(&resp),
			}
		}
	}
	return nil
}

func QueryTwBetaMindCheckList(
	baseUrl string, productModule string,
) (ResponseData[*pb.ListBlackBoxCaseTwBetaMindCheckResp], error) {
	params := map[string]string{
		"product_module": url.QueryEscape(productModule),
	}
	queryString := MapToQueryString(params, "")
	requestUrl := fmt.Sprintf("/api/beta-v1/checklist/list%s", queryString)

	var res ResponseData[*pb.ListBlackBoxCaseTwBetaMindCheckResp]
	err := ExecuteRequest(http.MethodGet, baseUrl, requestUrl, "", &res)
	if err != nil {
		return res, err
	}
	return res, nil
}

func QueryTwBetaMindList(baseUrl string, caseVersionId string) (ResponseData[[]pb.BlackBoxCaseTwBetaMind], error) {
	params := map[string]string{
		"case_version_id": caseVersionId,
	}
	var res ResponseData[[]pb.BlackBoxCaseTwBetaMind]
	err := ExecuteRequest(http.MethodGet, baseUrl, MapToQueryString(params, "/api/beta-v1/mind/list"), nil, &res)
	if err != nil {
		return res, err
	}
	return res, nil
}

func QueryTwBetaMindContent(baseUrl string, id int64, mindType string) (ResponseData[string], error) {
	params := map[string]string{
		"id": strconv.FormatInt(id, 10),
	}
	params["mind_type"] = mindType
	var res ResponseData[string]
	err := ExecuteRequest(http.MethodGet, baseUrl, MapToQueryString(params, "/api/beta-v1/mind/get"), nil, &res)
	if err != nil {
		return res, err
	}
	return res, nil
}

func QueryTwBetaCaseContent(baseUrl string, id uint64) (ResponseData[string], error) {
	params := map[string]string{
		"mind_id": strconv.FormatUint(id, 10),
	}
	var res ResponseData[string]
	err := ExecuteRequest(http.MethodGet, baseUrl, MapToQueryString(params, "/api/beta-v1/case/get"), nil, &res)
	if err != nil {
		return res, err
	}
	return res, nil
}

func UpdateTwBetaMindContent(baseUrl string, id uint64, mindContent string) (ResponseData[string], error) {
	params := map[string]string{
		"mind_id": strconv.FormatUint(id, 10),
	}
	params["mind_content"] = mindContent
	var res ResponseData[string]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/beta-v1/mind/update", params, &res)
	if err != nil {
		return res, err
	}
	return res, nil
}

func CreateTwBetaMind(baseUrl string, in *CreateTwBetaMindReq) (ResponseData[string], error) {
	var respData ResponseData[string]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/beta-v1/mind/create", in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func QueryTwBetaMindKnowledge(baseUrl string, caseId string) (ResponseData[*QueryTwBetaMindKnowledgeResp], error) {
	params := map[string]string{
		"case_id": caseId,
	}
	var res ResponseData[*QueryTwBetaMindKnowledgeResp]
	err := ExecuteRequest(
		http.MethodGet, baseUrl, MapToQueryString(params, "/api/beta-v1/mind/knowledge/get"), nil, &res,
	)
	if err != nil {
		return res, err
	}
	return res, nil
}

func CreateTwTestCase(baseUrl string, createReq *CreateTwCaseReq, stream *sse.Stream) error {
	bodyBytes, err := json.Marshal(createReq)
	if err != nil {
		setStreamError(stream, "接口异常，请稍后重试。")
		return err
	}

	req, err := http.NewRequest("POST", baseUrl+"/api/beta-v1/test-case/create", bytes.NewReader(bodyBytes))
	if err != nil {
		setStreamError(stream, "接口异常，请稍后重试。")
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	defer func(r *http.Response) {
		if r != nil {
			_ = r.Body.Close()
		}
	}(resp)
	if err != nil {
		setStreamError(stream, "接口异常，请稍后重试。")
		return fmt.Errorf("error sending: %s", err)
	}

	err = resp2Stream(resp, stream, "")
	if err != nil {
		return err
	}
	return nil
}
