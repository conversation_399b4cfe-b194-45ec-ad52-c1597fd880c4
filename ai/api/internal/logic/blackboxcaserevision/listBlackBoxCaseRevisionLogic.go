package blackboxcaserevision

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type ListBlackBoxCaseRevisionLogic struct {
	*logic.BaseLogic
}

// list black box case revision
func NewListBlackBoxCaseRevisionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseRevisionLogic {
	return &ListBlackBoxCaseRevisionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *ListBlackBoxCaseRevisionLogic) ListBlackBoxCaseRevision(req *types.ListBlackBoxCaseRevisionReq) (resp *types.ListBlackBoxCaseRevisionResp, err error) {
	in := &pb.ListBlackBoxCaseRevisionReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseRevisionServiceRpc.ListBlackBoxCaseRevision(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.ListBlackBoxCaseRevisionResp{Items: []*types.BlackBoxCaseRevision{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
