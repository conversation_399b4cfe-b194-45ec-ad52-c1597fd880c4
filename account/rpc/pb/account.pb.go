// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: account/account.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	rpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ColumnType int32

const (
	ColumnType_UNKNOWN   ColumnType = 0 // 留空
	ColumnType_VARCHAR   ColumnType = 1 // 对应go的string
	ColumnType_INT       ColumnType = 2 // 对应go的int64
	ColumnType_FLOAT     ColumnType = 3 // 对应go的float
	ColumnType_TINYINT   ColumnType = 4 // 对应go的int64
	ColumnType_DATETIME  ColumnType = 5 // 对应go的string，19位的字符串表示时间，如"2021-01-01 01:01:01"
	ColumnType_TIMESTAMP ColumnType = 6 // 对应go的int64，10位时间戳数字， 如1653642602，等价表示"1970-01-20 11:20:42"
)

// Enum value maps for ColumnType.
var (
	ColumnType_name = map[int32]string{
		0: "UNKNOWN",
		1: "VARCHAR",
		2: "INT",
		3: "FLOAT",
		4: "TINYINT",
		5: "DATETIME",
		6: "TIMESTAMP",
	}
	ColumnType_value = map[string]int32{
		"UNKNOWN":   0,
		"VARCHAR":   1,
		"INT":       2,
		"FLOAT":     3,
		"TINYINT":   4,
		"DATETIME":  5,
		"TIMESTAMP": 6,
	}
)

func (x ColumnType) Enum() *ColumnType {
	p := new(ColumnType)
	*p = x
	return p
}

func (x ColumnType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ColumnType) Descriptor() protoreflect.EnumDescriptor {
	return file_account_account_proto_enumTypes[0].Descriptor()
}

func (ColumnType) Type() protoreflect.EnumType {
	return &file_account_account_proto_enumTypes[0]
}

func (x ColumnType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ColumnType.Descriptor instead.
func (ColumnType) EnumDescriptor() ([]byte, []int) {
	return file_account_account_proto_rawDescGZIP(), []int{0}
}

type QueryAccountPoolEnvDataRequest struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	PoolEnvTable               string                 `protobuf:"bytes,1,opt,name=pool_env_table,json=poolEnvTable,proto3" json:"pool_env_table,omitempty"`                                                // 真实的池账号环境表名，如"t_tt_1637584556169"
	Condition                  *rpc.Condition         `protobuf:"bytes,2,opt,name=condition,proto3" json:"condition,omitempty"`                                                                            // 筛选条件
	SelectedColumnIdArray      []string               `protobuf:"bytes,3,rep,name=selected_column_id_array,json=selectedColumnIdArray,proto3" json:"selected_column_id_array,omitempty"`                   // 筛选的字段列表，不传递则表示只筛选account和password字段
	ExpectedCount              int64                  `protobuf:"varint,4,opt,name=expected_count,json=expectedCount,proto3" json:"expected_count,omitempty"`                                              // 预期筛选数量，正整数，不传递默认为1
	AllowLessThanExpectedCount int64                  `protobuf:"varint,5,opt,name=allow_less_than_expected_count,json=allowLessThanExpectedCount,proto3" json:"allow_less_than_expected_count,omitempty"` // 是否允许查询数据调数小于筛选数量， 1为允许，2为不允许，默认为不允许
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *QueryAccountPoolEnvDataRequest) Reset() {
	*x = QueryAccountPoolEnvDataRequest{}
	mi := &file_account_account_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryAccountPoolEnvDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAccountPoolEnvDataRequest) ProtoMessage() {}

func (x *QueryAccountPoolEnvDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_account_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAccountPoolEnvDataRequest.ProtoReflect.Descriptor instead.
func (*QueryAccountPoolEnvDataRequest) Descriptor() ([]byte, []int) {
	return file_account_account_proto_rawDescGZIP(), []int{0}
}

func (x *QueryAccountPoolEnvDataRequest) GetPoolEnvTable() string {
	if x != nil {
		return x.PoolEnvTable
	}
	return ""
}

func (x *QueryAccountPoolEnvDataRequest) GetCondition() *rpc.Condition {
	if x != nil {
		return x.Condition
	}
	return nil
}

func (x *QueryAccountPoolEnvDataRequest) GetSelectedColumnIdArray() []string {
	if x != nil {
		return x.SelectedColumnIdArray
	}
	return nil
}

func (x *QueryAccountPoolEnvDataRequest) GetExpectedCount() int64 {
	if x != nil {
		return x.ExpectedCount
	}
	return 0
}

func (x *QueryAccountPoolEnvDataRequest) GetAllowLessThanExpectedCount() int64 {
	if x != nil {
		return x.AllowLessThanExpectedCount
	}
	return 0
}

type QueryAccountPoolEnvDataResponse struct {
	state         protoimpl.MessageState                     `protogen:"open.v1"`
	ExpectedCount int64                                      `protobuf:"varint,1,opt,name=expected_count,json=expectedCount,proto3" json:"expected_count,omitempty"` // 预期筛选数量
	MatchCount    int64                                      `protobuf:"varint,2,opt,name=match_count,json=matchCount,proto3" json:"match_count,omitempty"`          // 实际筛选到的数据
	MatchData     []*QueryAccountPoolEnvDataResponse_Account `protobuf:"bytes,3,rep,name=match_data,json=matchData,proto3" json:"match_data,omitempty"`              // 实际筛选到数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryAccountPoolEnvDataResponse) Reset() {
	*x = QueryAccountPoolEnvDataResponse{}
	mi := &file_account_account_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryAccountPoolEnvDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAccountPoolEnvDataResponse) ProtoMessage() {}

func (x *QueryAccountPoolEnvDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_account_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAccountPoolEnvDataResponse.ProtoReflect.Descriptor instead.
func (*QueryAccountPoolEnvDataResponse) Descriptor() ([]byte, []int) {
	return file_account_account_proto_rawDescGZIP(), []int{1}
}

func (x *QueryAccountPoolEnvDataResponse) GetExpectedCount() int64 {
	if x != nil {
		return x.ExpectedCount
	}
	return 0
}

func (x *QueryAccountPoolEnvDataResponse) GetMatchCount() int64 {
	if x != nil {
		return x.MatchCount
	}
	return 0
}

func (x *QueryAccountPoolEnvDataResponse) GetMatchData() []*QueryAccountPoolEnvDataResponse_Account {
	if x != nil {
		return x.MatchData
	}
	return nil
}

type ReleaseTestAccountRequest struct {
	state                  protoimpl.MessageState                   `protogen:"open.v1"`
	ReleaseTasAccountArray []*ReleaseTestAccountRequest_PoolAccount `protobuf:"bytes,1,rep,name=release_tas_account_array,json=releaseTasAccountArray,proto3" json:"release_tas_account_array,omitempty"` // 被集合引用的真实tas测试账号数据
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ReleaseTestAccountRequest) Reset() {
	*x = ReleaseTestAccountRequest{}
	mi := &file_account_account_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseTestAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseTestAccountRequest) ProtoMessage() {}

func (x *ReleaseTestAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_account_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseTestAccountRequest.ProtoReflect.Descriptor instead.
func (*ReleaseTestAccountRequest) Descriptor() ([]byte, []int) {
	return file_account_account_proto_rawDescGZIP(), []int{2}
}

func (x *ReleaseTestAccountRequest) GetReleaseTasAccountArray() []*ReleaseTestAccountRequest_PoolAccount {
	if x != nil {
		return x.ReleaseTasAccountArray
	}
	return nil
}

type ReleaseTestAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReleaseTestAccountResponse) Reset() {
	*x = ReleaseTestAccountResponse{}
	mi := &file_account_account_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseTestAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseTestAccountResponse) ProtoMessage() {}

func (x *ReleaseTestAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_account_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseTestAccountResponse.ProtoReflect.Descriptor instead.
func (*ReleaseTestAccountResponse) Descriptor() ([]byte, []int) {
	return file_account_account_proto_rawDescGZIP(), []int{3}
}

type QueryAccountPoolEnvDataResponse_Column struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`                                                      // 字段名称
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`                                                      // 字段值
	LockValue     string                 `protobuf:"bytes,3,opt,name=lock_value,json=lockValue,proto3" json:"lock_value,omitempty"`                             // redis锁值
	ColumnType    ColumnType             `protobuf:"varint,4,opt,name=column_type,json=columnType,proto3,enum=account.ColumnType" json:"column_type,omitempty"` // 字段类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryAccountPoolEnvDataResponse_Column) Reset() {
	*x = QueryAccountPoolEnvDataResponse_Column{}
	mi := &file_account_account_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryAccountPoolEnvDataResponse_Column) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAccountPoolEnvDataResponse_Column) ProtoMessage() {}

func (x *QueryAccountPoolEnvDataResponse_Column) ProtoReflect() protoreflect.Message {
	mi := &file_account_account_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAccountPoolEnvDataResponse_Column.ProtoReflect.Descriptor instead.
func (*QueryAccountPoolEnvDataResponse_Column) Descriptor() ([]byte, []int) {
	return file_account_account_proto_rawDescGZIP(), []int{1, 0}
}

func (x *QueryAccountPoolEnvDataResponse_Column) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *QueryAccountPoolEnvDataResponse_Column) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *QueryAccountPoolEnvDataResponse_Column) GetLockValue() string {
	if x != nil {
		return x.LockValue
	}
	return ""
}

func (x *QueryAccountPoolEnvDataResponse_Column) GetColumnType() ColumnType {
	if x != nil {
		return x.ColumnType
	}
	return ColumnType_UNKNOWN
}

type QueryAccountPoolEnvDataResponse_Account struct {
	state         protoimpl.MessageState                    `protogen:"open.v1"`
	Account       []*QueryAccountPoolEnvDataResponse_Column `protobuf:"bytes,1,rep,name=account,proto3" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryAccountPoolEnvDataResponse_Account) Reset() {
	*x = QueryAccountPoolEnvDataResponse_Account{}
	mi := &file_account_account_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryAccountPoolEnvDataResponse_Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAccountPoolEnvDataResponse_Account) ProtoMessage() {}

func (x *QueryAccountPoolEnvDataResponse_Account) ProtoReflect() protoreflect.Message {
	mi := &file_account_account_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAccountPoolEnvDataResponse_Account.ProtoReflect.Descriptor instead.
func (*QueryAccountPoolEnvDataResponse_Account) Descriptor() ([]byte, []int) {
	return file_account_account_proto_rawDescGZIP(), []int{1, 1}
}

func (x *QueryAccountPoolEnvDataResponse_Account) GetAccount() []*QueryAccountPoolEnvDataResponse_Column {
	if x != nil {
		return x.Account
	}
	return nil
}

type ReleaseTestAccountRequest_Account struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       string                 `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	LockValue     string                 `protobuf:"bytes,2,opt,name=lock_value,json=lockValue,proto3" json:"lock_value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReleaseTestAccountRequest_Account) Reset() {
	*x = ReleaseTestAccountRequest_Account{}
	mi := &file_account_account_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseTestAccountRequest_Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseTestAccountRequest_Account) ProtoMessage() {}

func (x *ReleaseTestAccountRequest_Account) ProtoReflect() protoreflect.Message {
	mi := &file_account_account_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseTestAccountRequest_Account.ProtoReflect.Descriptor instead.
func (*ReleaseTestAccountRequest_Account) Descriptor() ([]byte, []int) {
	return file_account_account_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ReleaseTestAccountRequest_Account) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *ReleaseTestAccountRequest_Account) GetLockValue() string {
	if x != nil {
		return x.LockValue
	}
	return ""
}

type ReleaseTestAccountRequest_PoolAccount struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	PoolEnvTable  string                               `protobuf:"bytes,1,opt,name=pool_env_table,json=poolEnvTable,proto3" json:"pool_env_table,omitempty"` // 真实的池账号环境表名，如"t_tt_1637584556169"
	AccountArray  []*ReleaseTestAccountRequest_Account `protobuf:"bytes,2,rep,name=account_array,json=accountArray,proto3" json:"account_array,omitempty"`   // 池账号account字段列表，如["*********", "*********", "*********"]
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReleaseTestAccountRequest_PoolAccount) Reset() {
	*x = ReleaseTestAccountRequest_PoolAccount{}
	mi := &file_account_account_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseTestAccountRequest_PoolAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseTestAccountRequest_PoolAccount) ProtoMessage() {}

func (x *ReleaseTestAccountRequest_PoolAccount) ProtoReflect() protoreflect.Message {
	mi := &file_account_account_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseTestAccountRequest_PoolAccount.ProtoReflect.Descriptor instead.
func (*ReleaseTestAccountRequest_PoolAccount) Descriptor() ([]byte, []int) {
	return file_account_account_proto_rawDescGZIP(), []int{2, 1}
}

func (x *ReleaseTestAccountRequest_PoolAccount) GetPoolEnvTable() string {
	if x != nil {
		return x.PoolEnvTable
	}
	return ""
}

func (x *ReleaseTestAccountRequest_PoolAccount) GetAccountArray() []*ReleaseTestAccountRequest_Account {
	if x != nil {
		return x.AccountArray
	}
	return nil
}

var File_account_account_proto protoreflect.FileDescriptor

var file_account_account_proto_rawDesc = []byte{
	0x0a, 0x15, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x73, 0x71, 0x6c, 0x62, 0x75,
	0x69, 0x6c, 0x64, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xcf, 0x02, 0x0a, 0x1e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x76, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x0e, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x65, 0x6e,
	0x76, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x76, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x33, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x71, 0x6c, 0x62, 0x75, 0x69,
	0x6c, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09,
	0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x18, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x69, 0x64, 0x5f,
	0x61, 0x72, 0x72, 0x61, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x10, 0xfa, 0x42, 0x0d,
	0x92, 0x01, 0x0a, 0x18, 0x01, 0x22, 0x04, 0x72, 0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x15, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x49, 0x64, 0x41,
	0x72, 0x72, 0x61, 0x79, 0x12, 0x31, 0x0a, 0x0e, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x22, 0x05, 0x18, 0xe8, 0x07, 0x28, 0x01, 0x52, 0x0d, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x1e, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x5f, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x68, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x70, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x1a, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x4c,
	0x65, 0x73, 0x73, 0x54, 0x68, 0x61, 0x6e, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x9c, 0x03, 0x0a, 0x1f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x76, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x78, 0x70, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x4f, 0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f, 0x6f, 0x6c, 0x45,
	0x6e, 0x76, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74,
	0x61, 0x1a, 0x89, 0x01, 0x0a, 0x06, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x14, 0x0a, 0x05,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f,
	0x63, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x34, 0x0a, 0x0b, 0x63, 0x6f, 0x6c, 0x75, 0x6d,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0a, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x54, 0x0a,
	0x07, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x49, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x50, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x76, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x92, 0x03, 0x0a, 0x19, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x7a, 0x0a, 0x19, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x61, 0x73,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x72, 0x72, 0x61, 0x79, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x65, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x6f, 0x6f, 0x6c, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0x0a, 0x22, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x16, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x61,
	0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x72, 0x72, 0x61, 0x79, 0x1a, 0x57, 0x0a,
	0x07, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x0a, 0x6c,
	0x6f, 0x63, 0x6b, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x63,
	0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0x9f, 0x01, 0x0a, 0x0b, 0x50, 0x6f, 0x6f, 0x6c, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x0e, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x65,
	0x6e, 0x76, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x76,
	0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x61, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x61, 0x72, 0x72, 0x61, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a,
	0x10, 0xe8, 0x07, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x41, 0x72, 0x72, 0x61, 0x79, 0x22, 0x1c, 0x0a, 0x1a, 0x52, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x54, 0x65, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2a, 0x64, 0x0a, 0x0a, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x0b, 0x0a, 0x07, 0x56, 0x41, 0x52, 0x43, 0x48, 0x41, 0x52, 0x10, 0x01, 0x12, 0x07,
	0x0a, 0x03, 0x49, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x4c, 0x4f, 0x41, 0x54,
	0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x49, 0x4e, 0x59, 0x49, 0x4e, 0x54, 0x10, 0x04, 0x12,
	0x0c, 0x0a, 0x08, 0x44, 0x41, 0x54, 0x45, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x05, 0x12, 0x0d, 0x0a,
	0x09, 0x54, 0x49, 0x4d, 0x45, 0x53, 0x54, 0x41, 0x4d, 0x50, 0x10, 0x06, 0x32, 0xd6, 0x01, 0x0a,
	0x07, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x6c, 0x0a, 0x17, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x76, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x27, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f, 0x6f, 0x6c, 0x45, 0x6e,
	0x76, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x76, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x12, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x54, 0x65, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x23, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x54, 0x65, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e,
	0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74,
	0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62,
	0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_account_account_proto_rawDescOnce sync.Once
	file_account_account_proto_rawDescData = file_account_account_proto_rawDesc
)

func file_account_account_proto_rawDescGZIP() []byte {
	file_account_account_proto_rawDescOnce.Do(func() {
		file_account_account_proto_rawDescData = protoimpl.X.CompressGZIP(file_account_account_proto_rawDescData)
	})
	return file_account_account_proto_rawDescData
}

var file_account_account_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_account_account_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_account_account_proto_goTypes = []any{
	(ColumnType)(0),                                 // 0: account.ColumnType
	(*QueryAccountPoolEnvDataRequest)(nil),          // 1: account.QueryAccountPoolEnvDataRequest
	(*QueryAccountPoolEnvDataResponse)(nil),         // 2: account.QueryAccountPoolEnvDataResponse
	(*ReleaseTestAccountRequest)(nil),               // 3: account.ReleaseTestAccountRequest
	(*ReleaseTestAccountResponse)(nil),              // 4: account.ReleaseTestAccountResponse
	(*QueryAccountPoolEnvDataResponse_Column)(nil),  // 5: account.QueryAccountPoolEnvDataResponse.Column
	(*QueryAccountPoolEnvDataResponse_Account)(nil), // 6: account.QueryAccountPoolEnvDataResponse.Account
	(*ReleaseTestAccountRequest_Account)(nil),       // 7: account.ReleaseTestAccountRequest.Account
	(*ReleaseTestAccountRequest_PoolAccount)(nil),   // 8: account.ReleaseTestAccountRequest.PoolAccount
	(*rpc.Condition)(nil),                           // 9: sqlbuilder.Condition
}
var file_account_account_proto_depIdxs = []int32{
	9, // 0: account.QueryAccountPoolEnvDataRequest.condition:type_name -> sqlbuilder.Condition
	6, // 1: account.QueryAccountPoolEnvDataResponse.match_data:type_name -> account.QueryAccountPoolEnvDataResponse.Account
	8, // 2: account.ReleaseTestAccountRequest.release_tas_account_array:type_name -> account.ReleaseTestAccountRequest.PoolAccount
	0, // 3: account.QueryAccountPoolEnvDataResponse.Column.column_type:type_name -> account.ColumnType
	5, // 4: account.QueryAccountPoolEnvDataResponse.Account.account:type_name -> account.QueryAccountPoolEnvDataResponse.Column
	7, // 5: account.ReleaseTestAccountRequest.PoolAccount.account_array:type_name -> account.ReleaseTestAccountRequest.Account
	1, // 6: account.Account.QueryAccountPoolEnvData:input_type -> account.QueryAccountPoolEnvDataRequest
	3, // 7: account.Account.ReleaseTestAccount:input_type -> account.ReleaseTestAccountRequest
	2, // 8: account.Account.QueryAccountPoolEnvData:output_type -> account.QueryAccountPoolEnvDataResponse
	4, // 9: account.Account.ReleaseTestAccount:output_type -> account.ReleaseTestAccountResponse
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_account_account_proto_init() }
func file_account_account_proto_init() {
	if File_account_account_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_account_account_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_account_account_proto_goTypes,
		DependencyIndexes: file_account_account_proto_depIdxs,
		EnumInfos:         file_account_account_proto_enumTypes,
		MessageInfos:      file_account_account_proto_msgTypes,
	}.Build()
	File_account_account_proto = out.File
	file_account_account_proto_rawDesc = nil
	file_account_account_proto_goTypes = nil
	file_account_account_proto_depIdxs = nil
}
