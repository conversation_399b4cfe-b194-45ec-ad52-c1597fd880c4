package tasks

import (
	"context"
	"runtime/debug"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	accountlogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/internal/logic/account"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
)

var _ base.Handler = (*ProcessorReleaseTestAccount)(nil)

type ReleaseTestAccountTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewReleaseTestAccountTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReleaseTestAccountTaskLogic {
	return &ReleaseTestAccountTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReleaseTestAccountTaskLogic) Consume(payload []byte) (err error) {
	if len(payload) == 0 {
		return nil
	}
	l.Debugf("the payload of release test account: %s", payload)

	var info pb.ReleaseTestAccountRequest
	if err = protobuf.UnmarshalJSON(payload, &info); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of release test account, payload: %s, error: %+v",
			payload, err,
		)
	}

	_, err = accountlogic.NewReleaseTestAccountLogic(l.ctx, l.svcCtx).ReleaseTestAccount(&info)
	return err
}

type ProcessorReleaseTestAccount struct {
	svcCtx *svc.ServiceContext
}

func NewProcessorReleaseTestAccount(svcCtx *svc.ServiceContext) base.Handler {
	return &ProcessorReleaseTestAccount{
		svcCtx: svcCtx,
	}
}

func (p *ProcessorReleaseTestAccount) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	if err = NewReleaseTestAccountTaskLogic(ctx, p.svcCtx).Consume(task.Payload); err != nil {
		logger.Errorf("processor err: %+v", err)
		return nil, err
	}

	return []byte(constants.SUCCESS), nil
}
