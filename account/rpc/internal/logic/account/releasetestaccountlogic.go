package accountlogic

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb"
)

type ReleaseTestAccountLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewReleaseTestAccountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReleaseTestAccountLogic {
	return &ReleaseTestAccountLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *ReleaseTestAccountLogic) ReleaseTestAccount(in *pb.ReleaseTestAccountRequest) (
	*pb.ReleaseTestAccountResponse, error,
) {
	db, _ := l.svcCtx.DB.RawDB()

	for _, releaseTestAccount := range in.GetReleaseTasAccountArray() {
		poolEnvTable := releaseTestAccount.GetPoolEnvTable()
		accountArray := releaseTestAccount.GetAccountArray()
		count := len(accountArray)
		if count == 0 {
			continue
		}

		currentTimeStamp := time.Now().UnixMilli()
		l.Infof(
			"account开始释放账号, table: %s, count: %d, account[0]: %s, timestamp: %d",
			poolEnvTable, count, accountArray[0], currentTimeStamp,
		)

		// poolId, err := l.svcCtx.TPoolTableModel.FindPoolIdByPoolEnvTableName(l.ctx, poolEnvTable)
		poolId, flag := l.getPoolId(poolEnvTable)
		if poolId == 0 || !flag {
			continue // 账号池环境已经不存在或不可用
		}

		accounts := make([]string, 0, count)
		for _, account := range accountArray {
			accounts = append(accounts, account.GetAccount())
		}

		stmt, values, err := squirrel.Update(poolEnvTable).
			SetMap(
				squirrel.Eq{
					common.BuiltinTableFieldOfOccupyState: common.OccupyStateUnused,
					common.BuiltinTableFieldOfReturnTime:  currentTimeStamp,
				},
			).
			Where(
				squirrel.Eq{
					common.BuiltinTableFieldOfAccount: accounts,
				},
			).
			ToSql()
		if err != nil {
			return nil, err
		}

		err = caller.RetryWithOptionDo(
			func() error {
				_, err = db.ExecContext(l.ctx, stmt, values...)
				return err
			}, caller.WithRetry(3),
		)
		if err != nil {
			l.Errorf(
				"account释放账号失败, table: %s, count: %d, account[0]: %s, timestamp: %d, error: %+v",
				poolEnvTable, count, accountArray[0], currentTimeStamp, err,
			)
		} else {
			l.Infof(
				"account释放账号成功, table: %s, count: %d, account[0]: %s, timestamp: %d",
				poolEnvTable, count, accountArray[0], currentTimeStamp,
			)
		}

		for _, account := range accountArray {
			lockKey := fmt.Sprintf("%s%s_%s", common.AccountRedisKeyPrefix, poolEnvTable, account.GetAccount())
			lockValue := account.GetLockValue()
			lock := redislock.NewRedisLock(
				l.svcCtx.Redis, lockKey, redislock.WithValue(lockValue), redislock.WithExpire(10*time.Second),
			)
			releaseErr := lock.Release()
			if releaseErr != nil {
				re, ok := errorx.RootError(releaseErr)
				if !ok || re.Code() != errorx.ReleaseRedisLockFailure {
					l.Warnf("释放池账号redis锁发生异常: %+v", releaseErr)
				}
			}
		}
	}

	return &pb.ReleaseTestAccountResponse{}, nil
}

func (l *ReleaseTestAccountLogic) getPoolId(poolEnvTable string) (int64, bool) {
	var poolId int64
	templateTableIndex := strings.LastIndex(poolEnvTable, "_")
	templateTable := poolEnvTable[0:templateTableIndex]
	redisKey := fmt.Sprintf("pool_id_%s", templateTable)
	recordStr, redisErr := l.svcCtx.Redis.Get(redisKey)
	if redisErr == nil && recordStr != "" { // 从redis取到数据
		poolId, _ = strconv.ParseInt(recordStr, 10, 64)
	} else { // 从数据库读取数据
		var err error
		poolId, err = l.svcCtx.TPoolTableModel.FindPoolIdByPoolEnvTableName(l.ctx, poolEnvTable)
		if err != nil || poolId == 0 {
			return 0, false
		}

		recordStr = strconv.FormatInt(poolId, 10)
		_, err = redislock.NewRedisLockAndAcquire(
			l.svcCtx.Redis, redisKey, redislock.WithValue(recordStr), redislock.WithExpire(cacheExpireTimeOfOneDay),
		)
		if err != nil {
			l.Errorf("缓存账号池模板id到redis发生异常: %+v", err)
		}
	}

	return poolId, true
}
