// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: dispatcher.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	dispatcherlogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/logic/dispatcher"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

type DispatcherServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedDispatcherServer
}

func NewDispatcherServer(svcCtx *svc.ServiceContext) *DispatcherServer {
	return &DispatcherServer{
		svcCtx: svcCtx,
	}
}

func (s *DispatcherServer) Publish(ctx context.Context, in *pb.PublishReq) (*pb.PublishResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := dispatcherlogic.NewPublishLogic(ctx, s.svcCtx)

	return l.Publish(in)
}

func (s *DispatcherServer) Stop(ctx context.Context, in *pb.StopReq) (*pb.StopResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := dispatcherlogic.NewStopLogic(ctx, s.svcCtx)

	return l.Stop(in)
}

func (s *DispatcherServer) SearchTaskInfo(ctx context.Context, in *pb.SearchTaskInfoReq) (*pb.SearchTaskInfoResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := dispatcherlogic.NewSearchTaskInfoLogic(ctx, s.svcCtx)

	return l.SearchTaskInfo(in)
}
