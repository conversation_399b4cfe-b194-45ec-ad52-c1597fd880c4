package delcleanedtask

import (
	"context"
	"runtime/debug"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/logic/delcleanedtask"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/types"
)

type Processor struct {
	svcCtx *svc.ServiceContext
}

func (p *Processor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v, stack: %s", r, debug.Stack())
		}
	}()

	var data types.DelCleanedTask
	if err = jsonx.Unmarshal(task.Payload, &data); err != nil {
		return []byte(constants.FAILURE), errors.Wrapf(
			err, "failed to unmarshal task payload, payload: %s", task.Payload,
		)
	}

	if err = delcleanedtask.NewDelCleanedTasksLogic(ctx, p.svcCtx).DeleteCleanedRecords(&data); err != nil {
		logger.Errorf(
			"failed to delete cleaned records, clean_type: %s, keep_days: %d, error: %+v",
			data.CleanType, data.KeepDays, err,
		)
		return []byte(constants.FAILURE), nil
	}

	return []byte(constants.SUCCESS), nil
}

func NewProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &Processor{
		svcCtx: svcCtx,
	}
}
