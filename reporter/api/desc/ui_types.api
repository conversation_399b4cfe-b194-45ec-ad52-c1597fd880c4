syntax = "v1"

import "types.api"

info(
    title: "质量平台UI测试报告服务类型定义"
    desc: "质量平台UI测试报告服务中API接口涉及的类型定义"
    author: "heji<PERSON><PERSON>"
    email: "heji<PERSON><PERSON>@52tt.com"
    version: "0.1.0"
)

type UIPlanRecordItem {
    ProjectId         string        `json:"project_id"`
    PlanId            string        `json:"plan_id"`
    PlanName          string        `json:"plan_name"`
    TaskId            string        `json:"task_id"`
    ExecuteId         string        `json:"execute_id"`
    Status            string        `json:"status"`
    CostTime          int64         `json:"cost_time"`
    ExecutedBy        *FullUserInfo `json:"executed_by"`
    StartedAt         int64         `json:"started_at"`
    EndedAt           int64         `json:"ended_at"`
    TotalSuite        int64         `json:"total_suite"`
    FinishedSuite     int64         `json:"finished_suite"`
    SuccessSuite      int64         `json:"success_suite"`
    TotalCase         int64         `json:"total_case"`
    FinishedCase      int64         `json:"finished_case"`
    SuccessCase       int64         `json:"success_case"`
    Content           string        `json:"content"`
    ReportViewUrl     string        `json:"report_view_url"`
    ReportDownloadUrl string        `json:"report_download_url"`
    Finished          bool          `json:"finished"`
    Cleaned           bool          `json:"cleaned"`
    Type              string        `json:"type"`
    ExecuteStatus     int8          `json:"execute_status"`              //执行状态(0排队中,1执行中,2已完成,3已停止)
    PriorityType      int8          `json:"priority_type"`               //优先级策略(0 1 2 3 4 =》Default、Middle、High、Ultra、Low)
    ExecutedResult    int8          `json:"executed_result"`             //执行结果(0缺省,1成功,2失败,3异常)
    UpdateAt          int64         `json:"update_at" redis:"update_at"` // 更新时间
    WaitTime          int64         `json:"wait_time"`                   // 排队耗时
}

type UIPlanRecord {
    TaskId               string        `json:"task_id"`               // 任务ID
    ExecuteId            string        `json:"execute_id"`            // 执行ID

    ProjectId            string        `json:"project_id"`            // 项目ID
    PlanId               string        `json:"plan_id"`               // 计划ID
    PlanName             string        `json:"plan_name"`             // 计划名称
    Type                 string        `json:"type"`                  // 计划类型（手动、定时、接口）
    PriorityType         int8          `json:"priority_type"`         // 优先级
    GitConfig            *GitConfig    `json:"git_config"`            // Git配置信息
    DeviceType           int8          `json:"device_type"`           // 设备类型（真机、云手机）
    PlatformType         int8          `json:"platform_type"`         // 测试系统（Android、IOS）
    Devices              []string      `json:"devices"`               // 设备列表
    Together             bool          `json:"together"`              // 选择的设备是否一起执行
    PackageName          string        `json:"package_name"`          // 包名
    ExecutionEnvironment string        `json:"execution_environment"` // 执行环境
    CallbackUrl          string        `json:"callback_url"`          // 回调地址
    AppDownloadLink      string        `json:"app_download_link"`     // APP下载地址
    AppVersion           string        `json:"app_version"`           // APP版本
    AppName              string        `json:"app_name"`              // 测试应用名称
    TestLanguage         int8          `json:"test_language"`         // 测试语言
    TestLanguageVersion  string        `json:"test_language_version"` // 测试语言版本
    TestFramework        int8          `json:"test_framework"`        // 测试框架
    TestArgs             []string      `json:"test_args"`             // 附加参数

    Status               string        `json:"status"`                // 执行状态（结果）
    TotalSuite           int64         `json:"total_suite"`           // 总的集合数
    SuccessSuite         int64         `json:"success_suite"`         // 执行成功的集合数
    FailureSuite         int64         `json:"failure_suite"`         // 执行失败的集合数
    TotalCase            int64         `json:"total_case"`            // 总的用例数
    SuccessCase          int64         `json:"success_case"`          // 执行成功的用例数
    FailureCase          int64         `json:"failure_case"`          // 执行失败的用例数
    CostTime             int64         `json:"cost_time"`             // 耗时
    StartedAt            int64         `json:"started_at"`            // 开始时间
    EndedAt              int64         `json:"ended_at"`              // 结束时间
    ExecutedBy           *FullUserInfo `json:"executed_by"`           // 执行者
}

type GitConfig {
    ProjectId   string `json:"project_id"`
    ConfigId    string `json:"config_id"`
    Type        string `json:"type"`
    Name        string `json:"name"`
    Description string `json:"description"`
    Url         string `json:"url"`
    AccessToken string `json:"access_token"`
    Branch      string `json:"branch"`
}

type UISuiteRecord {
    TaskId        string        `json:"task_id"`         // 任务ID
    ExecuteId     string        `json:"execute_id"`      // UI集合执行ID
    PlanExecuteId string        `json:"plan_execute_id"` // UI计划执行ID
    ProjectId     string        `json:"project_id"`      // 项目ID
    SuiteId       string        `json:"suite_id"`        // 集合ID
    SuiteName     string        `json:"suite_name"`      // 集合名称
    Udid          string        `json:"udid"`            // 设备编号
    Status        string        `json:"status"`          // 执行状态（结果）
    TotalCase     int64         `json:"total_case"`      // 总的用例数
    SuccessCase   int64         `json:"success_case"`    // 执行成功的用例数
    FailureCase   int64         `json:"failure_case"`    // 执行失败的用例数
    CostTime      int64         `json:"cost_time"`       // 耗时
    StartedAt     int64         `json:"started_at"`      // 开始时间
    EndedAt       int64         `json:"ended_at"`        // 结束时间
    ExecutedBy    *FullUserInfo `json:"executed_by"`     // 执行者
}

type UICaseRecord {
    TaskId         string        `json:"task_id"`          // 任务ID
    ExecuteId      string        `json:"execute_id"`       // UI用例执行ID
    SuiteExecuteId string        `json:"suite_execute_id"` // UI集合执行ID
    ProjectId      string        `json:"project_id"`       // 项目ID
    CaseId         string        `json:"case_id"`          // 用例ID
    CaseName       string        `json:"case_name"`        // 用例名称
    Udid           string        `json:"udid"`             // 设备编号
    Status         string        `json:"status"`           // 执行状态（结果）
    CostTime       int64         `json:"cost_time"`        // 耗时
    StartedAt      int64         `json:"started_at"`       // 开始时间
    EndedAt        int64         `json:"ended_at"`         // 结束时间
    ExecutedBy     *FullUserInfo `json:"executed_by"`      // 执行者
}

type UICaseStep {
    TaskId    string `json:"task_id"`    // 任务ID
    StepId    string `json:"step_id"`    // 步骤ID
    Stage     int8   `json:"stage"`      // 阶段
    Index     int64   `json:"index"`     // 步骤索引
    Name      string `json:"name"`       // 步骤名称
    Status    string `json:"status"`     // 执行状态（结果）
    Content   string `json:"content"`    // 步骤内容
    StartedAt int64  `json:"started_at"` // 开始时间
    EndedAt   int64  `json:"ended_at"`   // 结束时间
}

type UIDeviceRecord {
    TaskId        string        `json:"task_id"`         // 任务ID
    PlanExecuteId string        `json:"plan_execute_id"` // UI计划执行ID
    ProjectId     string        `json:"project_id"`      // 项目ID
    Udid          string        `json:"udid"`            // 设备编号
    Status        string        `json:"status"`          // 执行状态（结果）
    TotalCase     int64         `json:"total_case"`      // 总的用例数
    SuccessCase   int64         `json:"success_case"`    // 执行成功的用例数
    FailureCase   int64         `json:"failure_case"`    // 执行失败的用例数
    CostTime      int64         `json:"cost_time"`       // 耗时
    StartedAt     int64         `json:"started_at"`      // 开始时间
    EndedAt       int64         `json:"ended_at"`        // 结束时间
    ExecutedBy    *FullUserInfo `json:"executed_by"`     // 执行者
}

type CompareSlaDataItem {
    TestValue string `json:"test_value"` // 测试版本数值
    BaseValue string `json:"base_value"` // 基准版本数值
    RateValue string `json:"rate_value"` // 测试版本增长率
    OverLimit bool   `json:"over_limit"` // 测试版本是否超过阈值
}

type CompareSlaDataRespItem {
    *Device // 设备信息
    Version CompareSlaDataItem `json:"version"` // 基线版本
    Counts CompareSlaDataItem `json:"counts"` // 测试次数
    AvgFinishLaunchedTime CompareSlaDataItem `json:"avg_finish_launched_time"` // 冷启动用时
    AvgAutoLoginTime CompareSlaDataItem `json:"avg_auto_login_time"` // 自动登录用时
    AvgNewHomePageTime CompareSlaDataItem `json:"avg_new_home_page_time"` // 启动到进入新版首页用时
}

type ListSlaDataRespItem {
    DeviceName string `json:"device_name"`
	Version string `json:"version"`
	FinishLaunchedTime int `json:"finish_launched_time"`
	AutoLogin int `json:"auto_login"`
	NewHomePage int `json:"new_home_page"`
	AppInitHomeProcessStart int `json:"app_init_home_process_start"`
	AppInitHomePreMain int `json:"app_init_home_pre_main"`
	AppInitHomeMainDefaultAgree int `json:"app_init_home_main_default_agree"`
	AppInitHomeMainSplashBg int `json:"app_init_home_main_splash_bg"`
	AppInitHomeCmdCore3093 int `json:"app_init_home_cmd_core_3093"`
	AppInitHomeCmd3093 int `json:"app_init_home_cmd_3093"`
	AppInitHomeMainShowSplashBg int `json:"app_init_home_main_show_splash_bg"`
	AppInitHomeMainSplash int `json:"app_init_home_main_splash"`
	AppInitHomeHomeFirstFrame int `json:"app_init_home_home_first_frame"`
	AppInitHomeHomeJingang int `json:"app_init_home_home_jingang"`
	AppInitHomeHomeJingangUI int `json:"app_init_home_home_jingang_ui"`
	AppInitHomeHomeListRoom int `json:"app_init_home_home_list_room"`
	AppInitHomeCmdCore31526 int `json:"app_init_home_cmd_core_31526"`
	AppInitHomeCmd31526 int `json:"app_init_home_cmd_31526"`
	AppInitHomeHomeListTab int `json:"app_init_home_home_list_tab"`
	AppInitHomeHomeList int `json:"app_init_home_home_list"`
	InitMainProcess int `json:"init_main_process"`
	InitMainProcessMid int `json:"init_main_process_mid"`
	AfterModuleInitBackTask int `json:"after_module_init_back_task"`
	AfterModuleInitMainTask int `json:"after_module_init_main_task"`
	ModuleInitBackTask int `json:"module_init_back_task"`
	ModuleInitBackTask2 int `json:"module_init_back_task_2"`
	InitMainTask int `json:"init_main_task"`
	ModuleInitBackTask1 int `json:"module_init_back_task_1"`
	InitBackTask int `json:"init_back_task"`
	PreModuleInitMainTask int `json:"pre_module_init_main_task"`
	CreateTime string `json:"create_time"`
}

type ListSlaVersionRespItem {
    Version string `json:"version"`
	Branch string `json:"branch"`
    Default bool `json:"default"`
}

// 计划执行记录列表
type (
    ListUIPlanRecordReq {
        ProjectId string `json:"project_id"`
        PlanId string `json:"plan_id"`
        Pagination *Pagination `json:"pagination,omitempty,optional"`
        Sort []*SortField `json:"sort,omitempty,optional"`
    }

    ListUIPlanRecordResp {
        CurrentPage uint64 `json:"current_page"`
        PageSize uint64 `json:"page_size"`
        TotalCount uint64 `json:"total_count"`
        TotalPage uint64 `json:"total_page"`
        Items []*UIPlanRecordItem `json:"items"`
    }
)

// 获取UI计划执行记录详情
type (
    GetUIPlanRecordReq {
        TaskId    string `form:"task_id" validate:"required" zh:"任务ID"`
        ExecuteId string `form:"execute_id" validate:"required" zh:"UI计划执行ID"`
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
    }
    GetUIPlanRecordResp {
        *UIPlanRecord
    }
)

// 搜索UI计划执行记录下的UI集合执行记录
type (
    SearchUISuiteRecordReq {
        TaskId     string       `json:"task_id" validate:"required" zh:"任务ID"`
        ExecuteId  string       `json:"execute_id" validate:"required" zh:"UI计划执行ID"`
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Udid       string       `json:"udid,omitempty,optional" validate:"omitempty,lte=64" zh:"设备编号"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchUISuiteRecordResp {
        CurrentPage uint64           `json:"current_page"`
        PageSize    uint64           `json:"page_size"`
        TotalCount  uint64           `json:"total_count"`
        TotalPage   uint64           `json:"total_page"`
        Items       []*UISuiteRecord `json:"items"`
    }
)

// 搜索UI集合执行记录下的UI用例执行记录
type (
    SearchUICaseRecordReq {
        TaskId     string       `json:"task_id" validate:"required" zh:"任务ID"`
        ExecuteId  string       `json:"execute_id" validate:"required" zh:"UI集合执行ID"`
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Udid       string       `json:"udid,omitempty,optional" validate:"omitempty,lte=64" zh:"设备编号"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchUICaseRecordResp {
        CurrentPage uint64          `json:"current_page"`
        PageSize    uint64          `json:"page_size"`
        TotalCount  uint64          `json:"total_count"`
        TotalPage   uint64          `json:"total_page"`
        Items       []*UICaseRecord `json:"items"`
    }
)

// 获取UI用例执行记录详情
type (
    GetUICaseRecordReq {
        TaskId    string `form:"task_id" validate:"required" zh:"任务ID"`
        ExecuteId string `form:"execute_id" validate:"required" zh:"UI用例执行ID"`
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
    }
    GetUICaseRecordResp {
        *UICaseRecord
    }
)

// 获取UI用例执行步骤列表
type (
    ListUICaseStepReq {
        TaskId      string `json:"task_id" validate:"required" zh:"任务ID"`
        ExecuteId   string `json:"execute_id" validate:"required" zh:"UI用例执行ID"`
        ProjectId   string `json:"project_id" validate:"required" zh:"项目ID"`
        WithContent bool   `json:"with_content,default=true" validate:"required" zh:"是否返回步骤内容"`
    }
    ListUICaseStepResp {
        TotalCount  uint64        `json:"total_count"`
        Items       []*UICaseStep `json:"items"`
    }
)

// 获取UI用例执行步骤
type (
    GetUICaseStepReq {
        TaskId string `form:"task_id" validate:"required" zh:"任务ID"`
        StepId string `form:"step_id" validate:"required" zh:"步骤ID"`
    }
    GetUICaseStepResp {
        *UICaseStep
    }
)

// 搜索UI计划执行记录下的设备记录
type (
    SearchUIDeviceRecordReq {
        TaskId     string       `json:"task_id" validate:"required" zh:"任务ID"`
        ExecuteId  string       `json:"execute_id" validate:"required" zh:"UI计划执行ID"`
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchUIDeviceRecordResp {
        CurrentPage uint64            `json:"current_page"`
        PageSize    uint64            `json:"page_size"`
        TotalCount  uint64            `json:"total_count"`
        TotalPage   uint64            `json:"total_page"`
        Items       []*UIDeviceRecord `json:"items"`
    }
)

type (
    GetUIDevicePerfDataReq {
        TaskId    string `form:"task_id" validate:"required" zh:"任务ID"`
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        Udid      string `form:"udid,omitempty,optional" validate:"omitempty,gte=1,lte=64" zh:"设备编号"`
        DataType  string `form:"data_type,default=CPU" validate:"required,oneof=CPU MEMORY FPS" zh:"数据类型"`
    }
    GetUIDevicePerfDataResp {
        *DevicePerfData
    }
)

// 获取SLA对比数据
type (
    CompareSlaDataReq {
        ProjectId string `json:"project_id,optional" zh:"项目ID"`
        Platform int8 `json:"platform" validate:"required,oneof=1 2" zh:"测试系统（Android、IOS）"`
        TaskId string `json:"task_id,optional" zh:"任务ID"`
        BaseTaskId string `json:"base_task_id,optional" zh:"基准任务ID"`
        Branch string `json:"branch,optional" validate:"omitempty,oneof=release testing" zh:"版本分支"`
        TestVersion string `json:"test_version" validate:"required" zh:"测试版本"`
        BaseVersion string `json:"base_version,optional" zh:"基准版本"`
        BaseLine string `json:"base_line,optional" zh:"性能阈值"`
    }
    CompareSlaDataResp {
        Items []*CompareSlaDataRespItem `json:"items" zh:"数据列表"`
    }
)

// 获取SLA详细数据
type (
    ListSlaDataReq {
        Platform int8 `json:"platform" validate:"required,oneof=1 2" zh:"测试系统（Android、IOS）"`
        TaskId string `json:"task_id,optional" zh:"任务ID"`
        Version string `json:"version" validate:"required" zh:"版本名称"`
        Udid string `json:"udid" validate:"required" zh:"设备编号"`
    }
    ListSlaDataResp {
        Items []*ListSlaDataRespItem `json:"items" zh:"数据列表"`
    }
)

// 获取指定分支的SLA版本列表
type (
    ListSlaVersionReq {
        Platform int8 `json:"platform" validate:"required,oneof=1 2" zh:"测试系统（Android、IOS）"`
        Branch string `json:"branch,optional" validate:"omitempty,oneof=release testing" zh:"版本分支"`
    }
    ListSlaVersionResp {
        Items []*ListSlaVersionRespItem `json:"items" zh:"数据列表"`
    }
)