package model

import (
	"context"

	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ StabilityDeviceExecutionStepContentModel = (*customStabilityDeviceExecutionStepContentModel)(nil)

	stabilityDeviceExecutionStepContentInsertFields = stringx.Remove(stabilityDeviceExecutionStepContentFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// StabilityDeviceExecutionStepContentModel is an interface to be customized, add more methods here,
	// and implement the added methods in customStabilityDeviceExecutionStepContentModel.
	StabilityDeviceExecutionStepContentModel interface {
		stabilityDeviceExecutionStepContentModel
		types.DBModel

		withSession(session sqlx.Session) StabilityDeviceExecutionStepContentModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *StabilityDeviceExecutionStepContent) squirrel.InsertBuilder
		UpdateBuilder(data *StabilityDeviceExecutionStepContent) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*StabilityDeviceExecutionStepContent, error)

		FindByTaskIDStepID(
			ctx context.Context, taskID, stepID string,
		) ([]*StabilityDeviceExecutionStepContent, error)
		DeleteRecordByTaskID(ctx context.Context, session sqlx.Session, taskID string) error
	}

	customStabilityDeviceExecutionStepContentModel struct {
		*defaultStabilityDeviceExecutionStepContentModel

		conn sqlx.SqlConn
	}
)

// NewStabilityDeviceExecutionStepContentModel returns a model for the database table.
func NewStabilityDeviceExecutionStepContentModel(conn sqlx.SqlConn) StabilityDeviceExecutionStepContentModel {
	return &customStabilityDeviceExecutionStepContentModel{
		defaultStabilityDeviceExecutionStepContentModel: newStabilityDeviceExecutionStepContentModel(conn),
		conn: conn,
	}
}

func (m *customStabilityDeviceExecutionStepContentModel) withSession(session sqlx.Session) StabilityDeviceExecutionStepContentModel {
	return NewStabilityDeviceExecutionStepContentModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customStabilityDeviceExecutionStepContentModel) Table() string {
	return m.table
}

func (m *customStabilityDeviceExecutionStepContentModel) Fields() []string {
	return stabilityDeviceExecutionStepContentFieldNames
}

func (m *customStabilityDeviceExecutionStepContentModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customStabilityDeviceExecutionStepContentModel) InsertBuilder(data *StabilityDeviceExecutionStepContent) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(stabilityDeviceExecutionStepContentInsertFields...).Values()
}

func (m *customStabilityDeviceExecutionStepContentModel) UpdateBuilder(data *StabilityDeviceExecutionStepContent) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customStabilityDeviceExecutionStepContentModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(stabilityDeviceExecutionStepContentFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customStabilityDeviceExecutionStepContentModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customStabilityDeviceExecutionStepContentModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customStabilityDeviceExecutionStepContentModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*StabilityDeviceExecutionStepContent, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*StabilityDeviceExecutionStepContent
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customStabilityDeviceExecutionStepContentModel) FindByTaskIDStepID(
	ctx context.Context, taskID, stepID string,
) ([]*StabilityDeviceExecutionStepContent, error) {
	sb := m.SelectBuilder().Where("`task_id` = ? AND `step_id` = ?", taskID, stepID).OrderBy("`index` ASC")
	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customStabilityDeviceExecutionStepContentModel) DeleteRecordByTaskID(
	ctx context.Context, session sqlx.Session, taskID string,
) error {
	// DELETE FROM `stability_device_execution_step_content` WHERE `task_id` = ?
	deleteBuilder := squirrel.Delete(m.table).Where("`task_id` = ?", taskID)
	stmt, values, err := deleteBuilder.ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
		return err
	}

	// DELETE FROM `stability_device_execution_step_content` WHERE `task_id` = ? LIMIT 500
	count := int64(common.ConstDefaultMaxDeleteItems)
	stmt, values, err = deleteBuilder.Limit(uint64(count)).ToSql()
	if err != nil {
		return err
	}

	for count >= common.ConstDefaultMaxDeleteItems {
		result, err := m.conn.ExecCtx(ctx, stmt, values...)
		if err != nil {
			return err
		}

		count, _ = result.RowsAffected()
	}

	return nil
}
