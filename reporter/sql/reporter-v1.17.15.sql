-- `UI`测试计划执行报告
TRUNCATE TABLE `ui_suite_execution_record`;
TRUNCATE TABLE `ui_case_execution_record`;
ALTER TABLE `ui_suite_execution_record` MODIFY `suite_id` VARCHAR(255) NOT NULL COMMENT '集合ID';
ALTER TABLE `ui_case_execution_record` MODIFY `case_id` VARCHAR(255) NOT NULL COMMENT '用例ID';
ALTER TABLE `case_fail_for_plan_stat` MODIFY `case_type` VARCHAR(64) NOT NULL COMMENT '用例类型[API_CASE，INTERFACE_CASE]';

CREATE TABLE IF NOT EXISTS `ui_case_execution_step`
(
    `id`         INT           NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `task_id`    VARCHAR(64)   NOT NULL COMMENT '任务ID',
    `execute_id` VARCHAR(64)   NOT NULL COMMENT '用例执行ID',
    `project_id` VARCHAR(64)   NOT NULL COMMENT '项目ID',
    `case_id`    VARCHAR(255)  NOT NULL COMMENT '用例ID',
    `step_id`    VARCHAR(64)   NOT NULL COMMENT '步骤ID',
    `stage`      TINYINT       NOT NULL DEFAULT 0 COMMENT '阶段（前置步骤、测试步骤、后置步骤）',
    `name`       VARCHAR(64)   NOT NULL COMMENT '步骤名称',
    `status`     VARCHAR(64)   NOT NULL COMMENT '状态（成功、失败）',
    `started_at` TIMESTAMP     NOT NULL COMMENT '开始时间',
    `ended_at`   TIMESTAMP     NOT NULL COMMENT '结束时间',
    `deleted`    TINYINT       NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by` VARCHAR(64)   NOT NULL COMMENT '创建者的用户ID',
    `updated_by` VARCHAR(64)   NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by` VARCHAR(64)   NULL COMMENT '删除者的用户ID',
    `created_at` TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP     NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_es_step_id` (`step_id`),
    KEY `ix_es_task_id_execute_id_project_id_case_id_stage_name` (`task_id`, `execute_id`, `project_id`, `case_id`, `stage`, `name`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'UI用例执行步骤表';

CREATE TABLE IF NOT EXISTS `ui_case_execution_step_content`
(
    `id`         INT           NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `task_id`    VARCHAR(64)   NOT NULL COMMENT '任务ID',
    `step_id`    VARCHAR(64)   NOT NULL COMMENT '步骤ID',
    `content`    VARCHAR(1024) NULL COMMENT '步骤分片内容',
    `index`      INT           NOT NULL DEFAULT 1 COMMENT '步骤分片内容索引',
    `deleted`    TINYINT       NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by` VARCHAR(64)   NOT NULL COMMENT '创建者的用户ID',
    `updated_by` VARCHAR(64)   NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by` VARCHAR(64)   NULL COMMENT '删除者的用户ID',
    `created_at` TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP     NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_esc_step_id_index` (`step_id`, `index`),
    KEY `ix_esc_task_id_step_id` (`task_id`, `step_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'UI用例执行步骤内容表';
