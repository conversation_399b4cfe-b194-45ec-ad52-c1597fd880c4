# This build file includes a target for the Ruby wrapper library for
# google-iam-credentials.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for iamcredentials.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "iamcredentials_ruby_wrapper",
    srcs = ["//google/iam/credentials/v1:credentials_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-iam-credentials",
        "ruby-cloud-env-prefix=IAM_CREDENTIALS",
        "ruby-cloud-wrapper-of=v1:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/iam",
        "ruby-cloud-api-id=iamcredentials.googleapis.com",
        "ruby-cloud-api-shortname=iamcredentials",
    ],
    ruby_cloud_description = "The Service Account Credentials API creates short-lived credentials for Identity and Access Management (IAM) service accounts. You can also use this API to sign JSON Web Tokens (JWTs), as well as blobs of binary data that contain other types of tokens.",
    ruby_cloud_title = "IAM Service Account Credentials",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-iam-credentials-ruby",
    deps = [
        ":iamcredentials_ruby_wrapper",
    ],
)
