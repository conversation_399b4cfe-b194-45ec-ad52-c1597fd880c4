// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.identity.accesscontextmanager.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Identity.AccessContextManager.V1";
option go_package = "cloud.google.com/go/accesscontextmanager/apiv1/accesscontextmanagerpb;accesscontextmanagerpb";
option java_multiple_files = true;
option java_outer_classname = "GcpUserAccessBindingProto";
option java_package = "com.google.identity.accesscontextmanager.v1";
option objc_class_prefix = "GACM";
option php_namespace = "Google\\Identity\\AccessContextManager\\V1";
option ruby_package = "Google::Identity::AccessContextManager::V1";

// Restricts access to Cloud Console and Google Cloud APIs for a set of users
// using Context-Aware Access.
message GcpUserAccessBinding {
  option (google.api.resource) = {
    type: "accesscontextmanager.googleapis.com/GcpUserAccessBinding"
    pattern: "organizations/{organization}/gcpUserAccessBindings/{gcp_user_access_binding}"
  };

  // Immutable. Assigned by the server during creation. The last segment has an arbitrary
  // length and has only URI unreserved characters (as defined by
  // [RFC 3986 Section 2.3](https://tools.ietf.org/html/rfc3986#section-2.3)).
  // Should not be specified by the client during creation.
  // Example: "organizations/256/gcpUserAccessBindings/b3-BhcX_Ud5N"
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Required. Immutable. Google Group id whose members are subject to this binding's restrictions.
  // See "id" in the [G Suite Directory API's Groups resource]
  // (https://developers.google.com/admin-sdk/directory/v1/reference/groups#resource).
  // If a group's email address/alias is changed, this resource will continue
  // to point at the changed group. This field does not accept group email
  // addresses or aliases.
  // Example: "01d520gv4vjcrht"
  string group_key = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. Access level that a user must have to be granted access. Only one access
  // level is supported, not multiple. This repeated field must have exactly
  // one element.
  // Example: "accessPolicies/9522/accessLevels/device_trusted"
  repeated string access_levels = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "accesscontextmanager.googleapis.com/AccessLevel"
    }
  ];
}
