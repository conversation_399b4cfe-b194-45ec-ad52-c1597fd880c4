// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routes.v1;

option csharp_namespace = "Google.Maps.Routes.V1";
option go_package = "cloud.google.com/go/maps/routes/apiv1/routespb;routespb";
option java_multiple_files = true;
option java_outer_classname = "VehicleEmissionTypeProto";
option java_package = "com.google.maps.routes.v1";
option objc_class_prefix = "GMRS";
option php_namespace = "Google\\Maps\\Routes\\V1";

// A set of values describing the vehicle's emission type.
// Applies only to the DRIVE travel mode.
enum VehicleEmissionType {
  // No emission type specified. Default to GASOLINE.
  VEHICLE_EMISSION_TYPE_UNSPECIFIED = 0;

  // Gasoline/petrol fueled vehicle.
  GASOLINE = 1;

  // Electricity powered vehicle.
  ELECTRIC = 2;

  // Hybrid fuel (such as gasoline + electric) vehicle.
  HYBRID = 3;
}
