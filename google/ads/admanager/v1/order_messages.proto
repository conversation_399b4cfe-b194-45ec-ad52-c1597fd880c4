// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/ads/admanager/v1/applied_label.proto";
import "google/ads/admanager/v1/custom_field_value.proto";
import "google/ads/admanager/v1/order_enums.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "OrderMessagesProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// The `Order` resource.
message Order {
  option (google.api.resource) = {
    type: "admanager.googleapis.com/Order"
    pattern: "networks/{network_code}/orders/{order}"
    plural: "orders"
    singular: "order"
  };

  // Identifier. The resource name of the `Order`.
  // Format: `networks/{network_code}/orders/{order_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Order ID.
  int64 order_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The display name of the Order.  This value has a maximum length
  // of 255 characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Specifies whether or not the Order is a programmatic order.
  bool programmatic = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. The resource name of the User responsible for trafficking the
  // Order. Format: "networks/{network_code}/users/{user_id}"
  string trafficker = 23 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "admanager.googleapis.com/User" }
  ];

  // Optional. The resource names of Contacts from the advertiser of this Order.
  // Format: "networks/{network_code}/contacts/{contact_id}"
  repeated string advertiser_contacts = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/Contact"
    }
  ];

  // Required. The resource name of the Company, which is of type
  // Company.Type.ADVERTISER, to which this order belongs. Format:
  // "networks/{network_code}/companies/{company_id}"
  string advertiser = 6 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/Company"
    }
  ];

  // Optional. The resource names of Contacts from the advertising Agency of
  // this Order. Format: "networks/{network_code}/contacts/{contact_id}"
  repeated string agency_contacts = 7 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/Contact"
    }
  ];

  // Optional. The resource name of the Company, which is of type
  // Company.Type.AGENCY, with which this order is associated. Format:
  // "networks/{network_code}/companies/{company_id}"
  string agency = 8 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "admanager.googleapis.com/Company"
    }
  ];

  // Optional. The resource names of Teams directly applied to this Order.
  // Format: "networks/{network_code}/teams/{team_id}"
  repeated string applied_teams = 9 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = { type: "admanager.googleapis.com/Team" }
  ];

  // Output only. The resource names of Teams applied to this Order including
  // inherited values. Format: "networks/{network_code}/teams/{team_id}"
  repeated string effective_teams = 28 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "admanager.googleapis.com/Team" }
  ];

  // Output only. The resource name of the User who created the Order on behalf
  // of the advertiser. This value is assigned by Google. Format:
  // "networks/{network_code}/users/{user_id}"
  string creator = 10 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "admanager.googleapis.com/User" }
  ];

  // Output only. The ISO 4217 3-letter currency code for the currency used by
  // the Order. This value is the network's currency code.
  string currency_code = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The instant at which the Order and its associated line items
  // are eligible to begin serving. This attribute is derived from the line item
  // of the order that has the earliest LineItem.start_time.
  google.protobuf.Timestamp start_time = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The instant at which the Order and its associated line items
  // stop being served. This attribute is derived from the line item of the
  // order that has the latest LineItem.end_time.
  google.protobuf.Timestamp end_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates whether or not this Order has an end time.
  bool unlimited_end_time = 45 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. An arbitrary ID to associate to the Order, which can be used as a
  // key to an external system.
  int64 external_order_id = 13 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The archival status of the Order.
  bool archived = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The application which modified this order. This attribute is
  // assigned by Google.
  string last_modified_by_app = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The instant this Order was last modified.
  google.protobuf.Timestamp update_time = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Provides any additional notes that may annotate the Order. This
  // attribute has a maximum length of 65,535 characters.
  string notes = 17 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The purchase order number for the Order. This value has a maximum
  // length of 63 characters.
  string po_number = 18 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The status of the Order.
  OrderStatusEnum.OrderStatus status = 20
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The resource name of the User responsible for the sales of the
  // Order. Format: "networks/{network_code}/users/{user_id}"
  string salesperson = 21 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = { type: "admanager.googleapis.com/User" }
  ];

  // Optional. Unordered list. The resource names of the secondary salespeople
  // associated with the order. Format:
  // "networks/{network_code}/users/{user_id}"
  repeated string secondary_salespeople = 22 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = UNORDERED_LIST,
    (google.api.resource_reference) = { type: "admanager.googleapis.com/User" }
  ];

  // Optional. Unordered list. The resource names of the secondary traffickers
  // associated with the order. Format:
  // "networks/{network_code}/users/{user_id}"
  repeated string secondary_traffickers = 24 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = UNORDERED_LIST,
    (google.api.resource_reference) = { type: "admanager.googleapis.com/User" }
  ];

  // Optional. The set of labels applied directly to this order.
  repeated AppliedLabel applied_labels = 25
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Contains the set of labels applied directly to the order as
  // well as those inherited from the company that owns the order. If a label
  // has been negated, only the negated label is returned. This field is
  // assigned by Google.
  repeated AppliedLabel effective_applied_labels = 26
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The set of custom field values to this order.
  repeated CustomFieldValue custom_field_values = 38
      [(google.api.field_behavior) = OPTIONAL];
}
