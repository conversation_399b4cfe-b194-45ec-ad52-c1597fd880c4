// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/enums/asset_field_type.proto";
import "google/ads/searchads360/v0/enums/product_channel.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "VisitProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the Visit resource.

// A visit.
message Visit {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/Visit"
    pattern: "customers/{customer_id}/visits/{ad_group_id}~{criteria_id}~{ds_visit_id}"
  };

  // Output only. The resource name of the visit.
  // Visit resource names have the form:
  //
  // `customers/{customer_id}/visits/{ad_group_id}~{criterion_id}~{ds_visit_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/Visit"
    }
  ];

  // Output only. The ID of the visit.
  optional int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Search Ads 360 keyword ID. A value of 0 indicates that the
  // keyword is unattributed.
  optional int64 criterion_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Search Ads 360 inventory account ID containing the product
  // that was clicked on. Search Ads 360 generates this ID when you link an
  // inventory account in Search Ads 360.
  optional int64 merchant_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Ad ID. A value of 0 indicates that the ad is unattributed.
  optional int64 ad_id = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A unique string for each visit that is passed to the landing
  // page as the click id URL parameter.
  optional string click_id = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp of the visit event. The timestamp is in the
  // customer's time zone and in "yyyy-MM-dd HH:mm:ss" format.
  optional string visit_date_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of the product clicked on.
  optional string product_id = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The sales channel of the product that was clicked on: Online
  // or Local.
  optional google.ads.searchads360.v0.enums.ProductChannelEnum.ProductChannel
      product_channel = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The language (ISO-639-1) that has been set for the Merchant
  // Center feed containing data about the product.
  optional string product_language_code = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The store in the Local Inventory Ad that was clicked on. This
  // should match the store IDs used in your local products feed.
  optional string product_store_id = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The country (ISO-3166 format) registered for the inventory
  // feed that contains the product clicked on.
  optional string product_country_code = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. ID of the asset which was interacted with during the visit
  // event.
  optional int64 asset_id = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Asset field type of the visit event.
  optional google.ads.searchads360.v0.enums.AssetFieldTypeEnum.AssetFieldType
      asset_field_type = 14 [(google.api.field_behavior) = OUTPUT_ONLY];
}
