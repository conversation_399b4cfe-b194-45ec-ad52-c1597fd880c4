// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "SimulationModificationMethodProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Proto file describing simulation modification methods.

// Container for enum describing the method by which a simulation modifies
// a field.
message SimulationModificationMethodEnum {
  // Enum describing the method by which a simulation modifies a field.
  enum SimulationModificationMethod {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The values in a simulation were applied to all children of a given
    // resource uniformly. Overrides on child resources were not respected.
    UNIFORM = 2;

    // The values in a simulation were applied to the given resource.
    // Overrides on child resources were respected, and traffic estimates
    // do not include these resources.
    DEFAULT = 3;

    // The values in a simulation were all scaled by the same factor.
    // For example, in a simulated TargetCpa campaign, the campaign target and
    // all ad group targets were scaled by a factor of X.
    SCALING = 4;
  }
}
