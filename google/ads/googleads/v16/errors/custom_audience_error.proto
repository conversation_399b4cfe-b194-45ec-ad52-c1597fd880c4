// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "CustomAudienceErrorProto";
option java_package = "com.google.ads.googleads.v16.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V16::Errors";

// Proto file describing custom audience errors.

// Container for enum describing possible custom audience errors.
message CustomAudienceErrorEnum {
  // Enum describing possible custom audience errors.
  enum CustomAudienceError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // New name in the custom audience is duplicated ignoring cases.
    NAME_ALREADY_USED = 2;

    // Cannot remove a custom audience while it's still being used as targeting.
    CANNOT_REMOVE_WHILE_IN_USE = 3;

    // Cannot update or remove a custom audience that is already removed.
    RESOURCE_ALREADY_REMOVED = 4;

    // The pair of [type, value] already exists in members.
    MEMBER_TYPE_AND_PARAMETER_ALREADY_EXISTED = 5;

    // Member type is invalid.
    INVALID_MEMBER_TYPE = 6;

    // Member type does not have associated value.
    MEMBER_TYPE_AND_VALUE_DOES_NOT_MATCH = 7;

    // Custom audience contains a member that violates policy.
    POLICY_VIOLATION = 8;

    // Change in custom audience type is not allowed.
    INVALID_TYPE_CHANGE = 9;
  }
}
