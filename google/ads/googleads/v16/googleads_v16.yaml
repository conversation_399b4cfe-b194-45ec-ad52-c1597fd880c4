type: google.api.Service
config_version: 3
name: googleads.googleapis.com
title: Google Ads API

apis:
- name: google.ads.googleads.v16.services.AccountBudgetProposalService
- name: google.ads.googleads.v16.services.AccountLinkService
- name: google.ads.googleads.v16.services.AdGroupAdLabelService
- name: google.ads.googleads.v16.services.AdGroupAdService
- name: google.ads.googleads.v16.services.AdGroupAssetService
- name: google.ads.googleads.v16.services.AdGroupAssetSetService
- name: google.ads.googleads.v16.services.AdGroupBidModifierService
- name: google.ads.googleads.v16.services.AdGroupCriterionCustomizerService
- name: google.ads.googleads.v16.services.AdGroupCriterionLabelService
- name: google.ads.googleads.v16.services.AdGroupCriterionService
- name: google.ads.googleads.v16.services.AdGroupCustomizerService
- name: google.ads.googleads.v16.services.AdGroupExtensionSettingService
- name: google.ads.googleads.v16.services.AdGroupFeedService
- name: google.ads.googleads.v16.services.AdGroupLabelService
- name: google.ads.googleads.v16.services.AdGroupService
- name: google.ads.googleads.v16.services.AdParameterService
- name: google.ads.googleads.v16.services.AdService
- name: google.ads.googleads.v16.services.AssetGroupAssetService
- name: google.ads.googleads.v16.services.AssetGroupListingGroupFilterService
- name: google.ads.googleads.v16.services.AssetGroupService
- name: google.ads.googleads.v16.services.AssetGroupSignalService
- name: google.ads.googleads.v16.services.AssetService
- name: google.ads.googleads.v16.services.AssetSetAssetService
- name: google.ads.googleads.v16.services.AssetSetService
- name: google.ads.googleads.v16.services.AudienceInsightsService
- name: google.ads.googleads.v16.services.AudienceService
- name: google.ads.googleads.v16.services.BatchJobService
- name: google.ads.googleads.v16.services.BiddingDataExclusionService
- name: google.ads.googleads.v16.services.BiddingSeasonalityAdjustmentService
- name: google.ads.googleads.v16.services.BiddingStrategyService
- name: google.ads.googleads.v16.services.BillingSetupService
- name: google.ads.googleads.v16.services.BrandSuggestionService
- name: google.ads.googleads.v16.services.CampaignAssetService
- name: google.ads.googleads.v16.services.CampaignAssetSetService
- name: google.ads.googleads.v16.services.CampaignBidModifierService
- name: google.ads.googleads.v16.services.CampaignBudgetService
- name: google.ads.googleads.v16.services.CampaignConversionGoalService
- name: google.ads.googleads.v16.services.CampaignCriterionService
- name: google.ads.googleads.v16.services.CampaignCustomizerService
- name: google.ads.googleads.v16.services.CampaignDraftService
- name: google.ads.googleads.v16.services.CampaignExtensionSettingService
- name: google.ads.googleads.v16.services.CampaignFeedService
- name: google.ads.googleads.v16.services.CampaignGroupService
- name: google.ads.googleads.v16.services.CampaignLabelService
- name: google.ads.googleads.v16.services.CampaignLifecycleGoalService
- name: google.ads.googleads.v16.services.CampaignService
- name: google.ads.googleads.v16.services.CampaignSharedSetService
- name: google.ads.googleads.v16.services.ConversionActionService
- name: google.ads.googleads.v16.services.ConversionAdjustmentUploadService
- name: google.ads.googleads.v16.services.ConversionCustomVariableService
- name: google.ads.googleads.v16.services.ConversionGoalCampaignConfigService
- name: google.ads.googleads.v16.services.ConversionUploadService
- name: google.ads.googleads.v16.services.ConversionValueRuleService
- name: google.ads.googleads.v16.services.ConversionValueRuleSetService
- name: google.ads.googleads.v16.services.CustomAudienceService
- name: google.ads.googleads.v16.services.CustomConversionGoalService
- name: google.ads.googleads.v16.services.CustomInterestService
- name: google.ads.googleads.v16.services.CustomerAssetService
- name: google.ads.googleads.v16.services.CustomerAssetSetService
- name: google.ads.googleads.v16.services.CustomerClientLinkService
- name: google.ads.googleads.v16.services.CustomerConversionGoalService
- name: google.ads.googleads.v16.services.CustomerCustomizerService
- name: google.ads.googleads.v16.services.CustomerExtensionSettingService
- name: google.ads.googleads.v16.services.CustomerFeedService
- name: google.ads.googleads.v16.services.CustomerLabelService
- name: google.ads.googleads.v16.services.CustomerLifecycleGoalService
- name: google.ads.googleads.v16.services.CustomerManagerLinkService
- name: google.ads.googleads.v16.services.CustomerNegativeCriterionService
- name: google.ads.googleads.v16.services.CustomerService
- name: google.ads.googleads.v16.services.CustomerSkAdNetworkConversionValueSchemaService
- name: google.ads.googleads.v16.services.CustomerUserAccessInvitationService
- name: google.ads.googleads.v16.services.CustomerUserAccessService
- name: google.ads.googleads.v16.services.CustomizerAttributeService
- name: google.ads.googleads.v16.services.ExperimentArmService
- name: google.ads.googleads.v16.services.ExperimentService
- name: google.ads.googleads.v16.services.ExtensionFeedItemService
- name: google.ads.googleads.v16.services.FeedItemService
- name: google.ads.googleads.v16.services.FeedItemSetLinkService
- name: google.ads.googleads.v16.services.FeedItemSetService
- name: google.ads.googleads.v16.services.FeedItemTargetService
- name: google.ads.googleads.v16.services.FeedMappingService
- name: google.ads.googleads.v16.services.FeedService
- name: google.ads.googleads.v16.services.GeoTargetConstantService
- name: google.ads.googleads.v16.services.GoogleAdsFieldService
- name: google.ads.googleads.v16.services.GoogleAdsService
- name: google.ads.googleads.v16.services.IdentityVerificationService
- name: google.ads.googleads.v16.services.InvoiceService
- name: google.ads.googleads.v16.services.KeywordPlanAdGroupKeywordService
- name: google.ads.googleads.v16.services.KeywordPlanAdGroupService
- name: google.ads.googleads.v16.services.KeywordPlanCampaignKeywordService
- name: google.ads.googleads.v16.services.KeywordPlanCampaignService
- name: google.ads.googleads.v16.services.KeywordPlanIdeaService
- name: google.ads.googleads.v16.services.KeywordPlanService
- name: google.ads.googleads.v16.services.KeywordThemeConstantService
- name: google.ads.googleads.v16.services.LabelService
- name: google.ads.googleads.v16.services.OfflineUserDataJobService
- name: google.ads.googleads.v16.services.PaymentsAccountService
- name: google.ads.googleads.v16.services.ProductLinkInvitationService
- name: google.ads.googleads.v16.services.ProductLinkService
- name: google.ads.googleads.v16.services.ReachPlanService
- name: google.ads.googleads.v16.services.RecommendationService
- name: google.ads.googleads.v16.services.RecommendationSubscriptionService
- name: google.ads.googleads.v16.services.RemarketingActionService
- name: google.ads.googleads.v16.services.SharedCriterionService
- name: google.ads.googleads.v16.services.SharedSetService
- name: google.ads.googleads.v16.services.SmartCampaignSettingService
- name: google.ads.googleads.v16.services.SmartCampaignSuggestService
- name: google.ads.googleads.v16.services.ThirdPartyAppAnalyticsLinkService
- name: google.ads.googleads.v16.services.TravelAssetSuggestionService
- name: google.ads.googleads.v16.services.UserDataService
- name: google.ads.googleads.v16.services.UserListService

types:
- name: google.ads.googleads.v16.errors.GoogleAdsFailure
- name: google.ads.googleads.v16.resources.BatchJob.BatchJobMetadata
- name: google.ads.googleads.v16.resources.OfflineUserDataJobMetadata
- name: google.ads.googleads.v16.services.PromoteExperimentMetadata
- name: google.ads.googleads.v16.services.ScheduleExperimentMetadata

documentation:
  summary: 'Manage your Google Ads accounts, campaigns, and reports with this API.'
  overview: |-
    The Google Ads API enables an app to integrate with the Google Ads
    platform. You can efficiently retrieve and change your Google Ads data
    using the API, making it ideal for managing large or complex accounts and
    campaigns.

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v16/{name=customers/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v16/{name=customers/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v16/{name=customers/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v16/{name=customers/*/operations}'
  - selector: google.longrunning.Operations.WaitOperation
    post: '/v16/{name=customers/*/operations/*}:wait'
    body: '*'

authentication:
  rules:
  - selector: google.ads.googleads.v16.services.AccountBudgetProposalService.MutateAccountBudgetProposal
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AccountLinkService.CreateAccountLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AccountLinkService.MutateAccountLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdGroupAdLabelService.MutateAdGroupAdLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdGroupAdService.MutateAdGroupAds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdGroupAssetService.MutateAdGroupAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdGroupAssetSetService.MutateAdGroupAssetSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdGroupBidModifierService.MutateAdGroupBidModifiers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdGroupCriterionCustomizerService.MutateAdGroupCriterionCustomizers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdGroupCriterionLabelService.MutateAdGroupCriterionLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdGroupCriterionService.MutateAdGroupCriteria
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdGroupCustomizerService.MutateAdGroupCustomizers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdGroupExtensionSettingService.MutateAdGroupExtensionSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdGroupFeedService.MutateAdGroupFeeds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdGroupLabelService.MutateAdGroupLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdGroupService.MutateAdGroups
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdParameterService.MutateAdParameters
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdService.GetAd
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AdService.MutateAds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AssetGroupAssetService.MutateAssetGroupAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AssetGroupListingGroupFilterService.MutateAssetGroupListingGroupFilters
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AssetGroupService.MutateAssetGroups
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AssetGroupSignalService.MutateAssetGroupSignals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AssetService.MutateAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AssetSetAssetService.MutateAssetSetAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AssetSetService.MutateAssetSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v16.services.AudienceInsightsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.AudienceService.MutateAudiences
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v16.services.BatchJobService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.BiddingDataExclusionService.MutateBiddingDataExclusions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.BiddingSeasonalityAdjustmentService.MutateBiddingSeasonalityAdjustments
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.BiddingStrategyService.MutateBiddingStrategies
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.BillingSetupService.MutateBillingSetup
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.BrandSuggestionService.SuggestBrands
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CampaignAssetService.MutateCampaignAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CampaignAssetSetService.MutateCampaignAssetSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CampaignBidModifierService.MutateCampaignBidModifiers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CampaignBudgetService.MutateCampaignBudgets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CampaignConversionGoalService.MutateCampaignConversionGoals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CampaignCriterionService.MutateCampaignCriteria
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CampaignCustomizerService.MutateCampaignCustomizers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v16.services.CampaignDraftService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CampaignExtensionSettingService.MutateCampaignExtensionSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CampaignFeedService.MutateCampaignFeeds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CampaignGroupService.MutateCampaignGroups
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CampaignLabelService.MutateCampaignLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CampaignLifecycleGoalService.ConfigureCampaignLifecycleGoals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CampaignService.MutateCampaigns
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CampaignSharedSetService.MutateCampaignSharedSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.ConversionActionService.MutateConversionActions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.ConversionAdjustmentUploadService.UploadConversionAdjustments
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.ConversionCustomVariableService.MutateConversionCustomVariables
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.ConversionGoalCampaignConfigService.MutateConversionGoalCampaignConfigs
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.ConversionUploadService.UploadCallConversions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.ConversionUploadService.UploadClickConversions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.ConversionValueRuleService.MutateConversionValueRules
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.ConversionValueRuleSetService.MutateConversionValueRuleSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomAudienceService.MutateCustomAudiences
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomConversionGoalService.MutateCustomConversionGoals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomInterestService.MutateCustomInterests
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomerAssetService.MutateCustomerAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomerAssetSetService.MutateCustomerAssetSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomerClientLinkService.MutateCustomerClientLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomerConversionGoalService.MutateCustomerConversionGoals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomerCustomizerService.MutateCustomerCustomizers
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomerExtensionSettingService.MutateCustomerExtensionSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomerFeedService.MutateCustomerFeeds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomerLabelService.MutateCustomerLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomerLifecycleGoalService.ConfigureCustomerLifecycleGoals
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomerManagerLinkService.MoveManagerLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomerManagerLinkService.MutateCustomerManagerLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomerNegativeCriterionService.MutateCustomerNegativeCriteria
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v16.services.CustomerService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomerSkAdNetworkConversionValueSchemaService.MutateCustomerSkAdNetworkConversionValueSchema
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomerUserAccessInvitationService.MutateCustomerUserAccessInvitation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomerUserAccessService.MutateCustomerUserAccess
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.CustomizerAttributeService.MutateCustomizerAttributes
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.ExperimentArmService.MutateExperimentArms
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v16.services.ExperimentService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.ExtensionFeedItemService.MutateExtensionFeedItems
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.FeedItemService.MutateFeedItems
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.FeedItemSetLinkService.MutateFeedItemSetLinks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.FeedItemSetService.MutateFeedItemSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.FeedItemTargetService.MutateFeedItemTargets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.FeedMappingService.MutateFeedMappings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.FeedService.MutateFeeds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.GeoTargetConstantService.SuggestGeoTargetConstants
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.GoogleAdsFieldService.GetGoogleAdsField
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.GoogleAdsFieldService.SearchGoogleAdsFields
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v16.services.GoogleAdsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.IdentityVerificationService.GetIdentityVerification
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.IdentityVerificationService.StartIdentityVerification
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.InvoiceService.ListInvoices
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.KeywordPlanAdGroupKeywordService.MutateKeywordPlanAdGroupKeywords
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.KeywordPlanAdGroupService.MutateKeywordPlanAdGroups
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.KeywordPlanCampaignKeywordService.MutateKeywordPlanCampaignKeywords
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.KeywordPlanCampaignService.MutateKeywordPlanCampaigns
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v16.services.KeywordPlanIdeaService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.KeywordPlanService.MutateKeywordPlans
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.KeywordThemeConstantService.SuggestKeywordThemeConstants
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.LabelService.MutateLabels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v16.services.OfflineUserDataJobService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.PaymentsAccountService.ListPaymentsAccounts
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v16.services.ProductLinkInvitationService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.ProductLinkService.CreateProductLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.ProductLinkService.RemoveProductLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v16.services.ReachPlanService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v16.services.RecommendationService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.RecommendationSubscriptionService.MutateRecommendationSubscription
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.RemarketingActionService.MutateRemarketingActions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.SharedCriterionService.MutateSharedCriteria
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.SharedSetService.MutateSharedSets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.SmartCampaignSettingService.GetSmartCampaignStatus
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.SmartCampaignSettingService.MutateSmartCampaignSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.ads.googleads.v16.services.SmartCampaignSuggestService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.ThirdPartyAppAnalyticsLinkService.RegenerateShareableLinkId
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.TravelAssetSuggestionService.SuggestTravelAssets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.UserDataService.UploadUserData
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: google.ads.googleads.v16.services.UserListService.MutateUserLists
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/adwords
