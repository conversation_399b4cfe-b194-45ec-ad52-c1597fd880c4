// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "ProductLinkInvitationErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing ProductLinkInvitation errors.

// Container for enum describing possible product link invitation errors.
message ProductLinkInvitationErrorEnum {
  // Enum describing possible product link invitation errors.
  enum ProductLinkInvitationError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in the version.
    UNKNOWN = 1;

    // The invitation status is invalid.
    INVALID_STATUS = 2;

    // The customer doesn't have the permission to perform this action
    PERMISSION_DENIED = 3;

    // An invitation could not be created, since the user already has admin
    // access to the invited account. Use the ProductLinkService to directly
    // create an active link.
    NO_INVITATION_REQUIRED = 4;

    // The customer is not permitted to create the invitation.
    CUSTOMER_NOT_PERMITTED_TO_CREATE_INVITATION = 5;
  }
}
