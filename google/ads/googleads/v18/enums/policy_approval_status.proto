// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "PolicyApprovalStatusProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing policy approval statuses.

// Container for enum describing possible policy approval statuses.
message PolicyApprovalStatusEnum {
  // The possible policy approval statuses. When there are several approval
  // statuses available the most severe one will be used. The order of severity
  // is DISAPPROVED, AREA_OF_INTEREST_ONLY, APPROVED_LIMITED and APPROVED.
  enum PolicyApprovalStatus {
    // No value has been specified.
    UNSPECIFIED = 0;

    // The received value is not known in this version.
    //
    // This is a response-only value.
    UNKNOWN = 1;

    // Will not serve.
    DISAPPROVED = 2;

    // Serves with restrictions.
    APPROVED_LIMITED = 3;

    // Serves without restrictions.
    APPROVED = 4;

    // Will not serve in targeted countries, but may serve for users who are
    // searching for information about the targeted countries.
    AREA_OF_INTEREST_ONLY = 5;
  }
}
