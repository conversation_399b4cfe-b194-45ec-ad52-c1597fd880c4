// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "KeywordPlanAdGroupProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the keyword plan ad group resource.

// A Keyword Planner ad group.
// Max number of keyword plan ad groups per plan: 200.
message KeywordPlanAdGroup {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/KeywordPlanAdGroup"
    pattern: "customers/{customer_id}/keywordPlanAdGroups/{keyword_plan_ad_group_id}"
  };

  // Immutable. The resource name of the Keyword Planner ad group.
  // KeywordPlanAdGroup resource names have the form:
  //
  // `customers/{customer_id}/keywordPlanAdGroups/{kp_ad_group_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/KeywordPlanAdGroup"
    }
  ];

  // The keyword plan campaign to which this ad group belongs.
  optional string keyword_plan_campaign = 6 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/KeywordPlanCampaign"
  }];

  // Output only. The ID of the keyword plan ad group.
  optional int64 id = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The name of the keyword plan ad group.
  //
  // This field is required and should not be empty when creating keyword plan
  // ad group.
  optional string name = 8;

  // A default ad group max cpc bid in micros in account currency for all
  // biddable keywords under the keyword plan ad group.
  // If not set, will inherit from parent campaign.
  optional int64 cpc_bid_micros = 9;
}
