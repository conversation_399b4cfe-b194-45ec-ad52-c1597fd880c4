// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.logging.v2;

import "google/api/field_behavior.proto";
import "google/api/monitored_resource.proto";
import "google/api/resource.proto";
import "google/logging/type/http_request.proto";
import "google/logging/type/log_severity.proto";
import "google/protobuf/any.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Logging.V2";
option go_package = "cloud.google.com/go/logging/apiv2/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LogEntryProto";
option java_package = "com.google.logging.v2";
option php_namespace = "Google\\Cloud\\Logging\\V2";
option ruby_package = "Google::Cloud::Logging::V2";

// An individual entry in a log.
message LogEntry {
  option (google.api.resource) = {
    type: "logging.googleapis.com/Log"
    pattern: "projects/{project}/logs/{log}"
    pattern: "organizations/{organization}/logs/{log}"
    pattern: "folders/{folder}/logs/{log}"
    pattern: "billingAccounts/{billing_account}/logs/{log}"
    name_field: "log_name"
  };

  // Required. The resource name of the log to which this log entry belongs:
  //
  //     "projects/[PROJECT_ID]/logs/[LOG_ID]"
  //     "organizations/[ORGANIZATION_ID]/logs/[LOG_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/logs/[LOG_ID]"
  //     "folders/[FOLDER_ID]/logs/[LOG_ID]"
  //
  // A project number may be used in place of PROJECT_ID. The project number is
  // translated to its corresponding PROJECT_ID internally and the `log_name`
  // field will contain PROJECT_ID in queries and exports.
  //
  // `[LOG_ID]` must be URL-encoded within `log_name`. Example:
  // `"organizations/**********/logs/cloudresourcemanager.googleapis.com%2Factivity"`.
  //
  // `[LOG_ID]` must be less than 512 characters long and can only include the
  // following characters: upper and lower case alphanumeric characters,
  // forward-slash, underscore, hyphen, and period.
  //
  // For backward compatibility, if `log_name` begins with a forward-slash, such
  // as `/projects/...`, then the log entry is ingested as usual, but the
  // forward-slash is removed. Listing the log entry will not show the leading
  // slash and filtering for a log name with a leading slash will never return
  // any results.
  string log_name = 12 [(google.api.field_behavior) = REQUIRED];

  // Required. The monitored resource that produced this log entry.
  //
  // Example: a log entry that reports a database error would be associated with
  // the monitored resource designating the particular database that reported
  // the error.
  google.api.MonitoredResource resource = 8
      [(google.api.field_behavior) = REQUIRED];

  // The log entry payload, which can be one of multiple types.
  oneof payload {
    // The log entry payload, represented as a protocol buffer. Some Google
    // Cloud Platform services use this field for their log entry payloads.
    //
    // The following protocol buffer types are supported; user-defined types
    // are not supported:
    //
    //   "type.googleapis.com/google.cloud.audit.AuditLog"
    //   "type.googleapis.com/google.appengine.logging.v1.RequestLog"
    google.protobuf.Any proto_payload = 2;

    // The log entry payload, represented as a Unicode string (UTF-8).
    string text_payload = 3;

    // The log entry payload, represented as a structure that is
    // expressed as a JSON object.
    google.protobuf.Struct json_payload = 6;
  }

  // Optional. The time the event described by the log entry occurred. This time
  // is used to compute the log entry's age and to enforce the logs retention
  // period. If this field is omitted in a new log entry, then Logging assigns
  // it the current time. Timestamps have nanosecond accuracy, but trailing
  // zeros in the fractional seconds might be omitted when the timestamp is
  // displayed.
  //
  // Incoming log entries must have timestamps that don't exceed the
  // [logs retention
  // period](https://cloud.google.com/logging/quotas#logs_retention_periods) in
  // the past, and that don't exceed 24 hours in the future. Log entries outside
  // those time boundaries aren't ingested by Logging.
  google.protobuf.Timestamp timestamp = 9
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time the log entry was received by Logging.
  google.protobuf.Timestamp receive_timestamp = 24
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The severity of the log entry. The default value is
  // `LogSeverity.DEFAULT`.
  google.logging.type.LogSeverity severity = 10
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A unique identifier for the log entry. If you provide a value,
  // then Logging considers other log entries in the same project, with the same
  // `timestamp`, and with the same `insert_id` to be duplicates which are
  // removed in a single query result. However, there are no guarantees of
  // de-duplication in the export of logs.
  //
  // If the `insert_id` is omitted when writing a log entry, the Logging API
  // assigns its own unique identifier in this field.
  //
  // In queries, the `insert_id` is also used to order log entries that have
  // the same `log_name` and `timestamp` values.
  string insert_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Information about the HTTP request associated with this log
  // entry, if applicable.
  google.logging.type.HttpRequest http_request = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A map of key, value pairs that provides additional information
  // about the log entry. The labels can be user-defined or system-defined.
  //
  // User-defined labels are arbitrary key, value pairs that you can use to
  // classify logs.
  //
  // System-defined labels are defined by GCP services for platform logs.
  // They have two components - a service namespace component and the
  // attribute name. For example: `compute.googleapis.com/resource_name`.
  //
  // Cloud Logging truncates label keys that exceed 512 B and label
  // values that exceed 64 KB upon their associated log entry being
  // written. The truncation is indicated by an ellipsis at the
  // end of the character string.
  map<string, string> labels = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Information about an operation associated with the log entry, if
  // applicable.
  LogEntryOperation operation = 15 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The REST resource name of the trace being written to
  // [Cloud Trace](https://cloud.google.com/trace) in
  // association with this log entry. For example, if your trace data is stored
  // in the Cloud project "my-trace-project" and if the service that is creating
  // the log entry receives a trace header that includes the trace ID "12345",
  // then the service should use "projects/my-tracing-project/traces/12345".
  //
  // The `trace` field provides the link between logs and traces. By using
  // this field, you can navigate from a log entry to a trace.
  string trace = 22 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The ID of the [Cloud Trace](https://cloud.google.com/trace) span
  // associated with the current operation in which the log is being written.
  // For example, if a span has the REST resource name of
  // "projects/some-project/traces/some-trace/spans/some-span-id", then the
  // `span_id` field is "some-span-id".
  //
  // A
  // [Span](https://cloud.google.com/trace/docs/reference/v2/rest/v2/projects.traces/batchWrite#Span)
  // represents a single operation within a trace. Whereas a trace may involve
  // multiple different microservices running on multiple different machines,
  // a span generally corresponds to a single logical operation being performed
  // in a single instance of a microservice on one specific machine. Spans
  // are the nodes within the tree that is a trace.
  //
  // Applications that are [instrumented for
  // tracing](https://cloud.google.com/trace/docs/setup) will generally assign a
  // new, unique span ID on each incoming request. It is also common to create
  // and record additional spans corresponding to internal processing elements
  // as well as issuing requests to dependencies.
  //
  // The span ID is expected to be a 16-character, hexadecimal encoding of an
  // 8-byte array and should not be zero. It should be unique within the trace
  // and should, ideally, be generated in a manner that is uniformly random.
  //
  // Example values:
  //
  //   - `000000000000004a`
  //   - `7a2190356c3fc94b`
  //   - `0000f00300090021`
  //   - `d39223e101960076`
  string span_id = 27 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The sampling decision of the trace associated with the log entry.
  //
  // True means that the trace resource name in the `trace` field was sampled
  // for storage in a trace backend. False means that the trace was not sampled
  // for storage when this log entry was written, or the sampling decision was
  // unknown at the time. A non-sampled `trace` value is still useful as a
  // request correlation identifier. The default is False.
  bool trace_sampled = 30 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Source code location information associated with the log entry,
  // if any.
  LogEntrySourceLocation source_location = 23
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Information indicating this LogEntry is part of a sequence of
  // multiple log entries split from a single LogEntry.
  LogSplit split = 35 [(google.api.field_behavior) = OPTIONAL];
}

// Additional information about a potentially long-running operation with which
// a log entry is associated.
message LogEntryOperation {
  // Optional. An arbitrary operation identifier. Log entries with the same
  // identifier are assumed to be part of the same operation.
  string id = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An arbitrary producer identifier. The combination of `id` and
  // `producer` must be globally unique. Examples for `producer`:
  // `"MyDivision.MyBigCompany.com"`, `"github.com/MyProject/MyApplication"`.
  string producer = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Set this to True if this is the first log entry in the operation.
  bool first = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Set this to True if this is the last log entry in the operation.
  bool last = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Additional information about the source code location that produced the log
// entry.
message LogEntrySourceLocation {
  // Optional. Source file name. Depending on the runtime environment, this
  // might be a simple name or a fully-qualified name.
  string file = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Line within the source file. 1-based; 0 indicates no line number
  // available.
  int64 line = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Human-readable name of the function or method being invoked, with
  // optional context such as the class or package name. This information may be
  // used in contexts such as the logs viewer, where a file and line number are
  // less meaningful. The format can vary by language. For example:
  // `qual.if.ied.Class.method` (Java), `dir/package.func` (Go), `function`
  // (Python).
  string function = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Additional information used to correlate multiple log entries. Used when a
// single LogEntry would exceed the Google Cloud Logging size limit and is
// split across multiple log entries.
message LogSplit {
  // A globally unique identifier for all log entries in a sequence of split log
  // entries. All log entries with the same |LogSplit.uid| are assumed to be
  // part of the same sequence of split log entries.
  string uid = 1;

  // The index of this LogEntry in the sequence of split log entries. Log
  // entries are given |index| values 0, 1, ..., n-1 for a sequence of n log
  // entries.
  int32 index = 2;

  // The total number of log entries that the original LogEntry was split into.
  int32 total_splits = 3;
}
