# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_grpc_library",
    "java_proto_library",
    "php_proto_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "calendar_proto",
    srcs = [
        "calendar_addon_manifest.proto",
    ],
    deps = [
        "//google/api:field_behavior_proto",
        "//google/apps/script/type:type_proto",
    ],
)

java_proto_library(
    name = "calendar_java_proto",
    deps = [":calendar_proto"],
)

java_grpc_library(
    name = "calendar_java_grpc",
    srcs = [":calendar_proto"],
    deps = [":calendar_java_proto"],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate java files for these protos.
# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-apps-script-type-calendar-java",
    transport = "grpc+rest",
    deps = [
        ":calendar_java_grpc",
        ":calendar_java_proto",
        ":calendar_proto",
    ],
)

go_proto_library(
    name = "calendar_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/apps/script/type/calendar",
    protos = [":calendar_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/apps/script/type:type_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_proto_library",
)

py_proto_library(
    name = "calendar_py_proto",
    deps = [":calendar_proto"],
)

py_gapic_library(
    name = "calendar_py_gapic",
    srcs = [":calendar_proto"],
    rest_numeric_enums = False,
    transport = "grpc",
    opt_args = [
        "proto-plus-deps=google.apps.script.type",
    ],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "calendar-py",
    deps = [
        ":calendar_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
php_proto_library(
    name = "calendar_php_proto",
    deps = [":calendar_proto"],
)

ruby_proto_library(
    name = "calendar_ruby_proto",
    deps = [":calendar_proto"],
)

ruby_grpc_library(
    name = "calendar_ruby_grpc",
    srcs = [":calendar_proto"],
    deps = [":calendar_ruby_proto"],
)

csharp_proto_library(
    name = "calendar_csharp_proto",
    deps = [":calendar_proto"],
)

csharp_grpc_library(
    name = "calendar_csharp_grpc",
    srcs = [":calendar_proto"],
    deps = [":calendar_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
# Put your C++ code here
