// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "LocationMetadataProto";
option java_package = "com.google.cloud.bigquery.v2";

// BigQuery-specific metadata about a location. This will be set on
// google.cloud.location.Location.metadata in Cloud Location API
// responses.
message LocationMetadata {
  // The legacy BigQuery location ID, e.g. “EU” for the “europe” location.
  // This is for any API consumers that need the legacy “US” and “EU” locations.
  string legacy_location_id = 1;
}
