// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_multiple_files = true;
option java_outer_classname = "DecimalTargetTypesProto";
option java_package = "com.google.cloud.bigquery.v2";

// The data types that could be used as a target type when converting decimal
// values.
enum DecimalTargetType {
  // Invalid type.
  DECIMAL_TARGET_TYPE_UNSPECIFIED = 0;

  // Decimal values could be converted to NUMERIC
  // type.
  NUMERIC = 1;

  // Decimal values could be converted to BIGNUMERIC
  // type.
  BIGNUMERIC = 2;

  // Decimal values could be converted to STRING type.
  STRING = 3;
}
