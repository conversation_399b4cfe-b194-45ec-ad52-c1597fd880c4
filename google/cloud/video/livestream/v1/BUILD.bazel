# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "livestream_proto",
    srcs = [
        "outputs.proto",
        "resources.proto",
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:datetime_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "livestream_proto_with_info",
    deps = [
        ":livestream_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "livestream_java_proto",
    deps = [":livestream_proto"],
)

java_grpc_library(
    name = "livestream_java_grpc",
    srcs = [":livestream_proto"],
    deps = [":livestream_java_proto"],
)

java_gapic_library(
    name = "livestream_java_gapic",
    srcs = [":livestream_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "livestream_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "livestream_v1.yaml",
    test_deps = [
        ":livestream_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":livestream_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "livestream_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.video.livestream.v1.LivestreamServiceClientHttpJsonTest",
        "com.google.cloud.video.livestream.v1.LivestreamServiceClientTest",
    ],
    runtime_deps = [":livestream_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-video-livestream-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":livestream_java_gapic",
        ":livestream_java_grpc",
        ":livestream_java_proto",
        ":livestream_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "livestream_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/video/livestream/apiv1/livestreampb",
    protos = [":livestream_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:datetime_go_proto",
    ],
)

go_gapic_library(
    name = "livestream_go_gapic",
    srcs = [":livestream_proto_with_info"],
    grpc_service_config = "livestream_grpc_service_config.json",
    importpath = "cloud.google.com/go/video/livestream/apiv1;livestream",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "livestream_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":livestream_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-video-livestream-v1-go",
    deps = [
        ":livestream_go_gapic",
        ":livestream_go_gapic_srcjar-metadata.srcjar",
        ":livestream_go_gapic_srcjar-snippets.srcjar",
        ":livestream_go_gapic_srcjar-test.srcjar",
        ":livestream_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "livestream_py_gapic",
    srcs = [":livestream_proto"],
    grpc_service_config = "livestream_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=live_stream",
        "python-gapic-namespace=google.cloud.video",
        "warehouse-package-name=google-cloud-video-live-stream",
    ],
    rest_numeric_enums = True,
    service_yaml = "livestream_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "livestream_py_gapic_test",
    srcs = [
        "livestream_py_gapic_pytest.py",
        "livestream_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":livestream_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "video-livestream-v1-py",
    deps = [
        ":livestream_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "livestream_php_proto",
    deps = [":livestream_proto"],
)

php_gapic_library(
    name = "livestream_php_gapic",
    srcs = [":livestream_proto_with_info"],
    grpc_service_config = "livestream_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "livestream_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":livestream_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-video-livestream-v1-php",
    deps = [
        ":livestream_php_gapic",
        ":livestream_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "livestream_nodejs_gapic",
    package_name = "@google-cloud/livestream",
    src = ":livestream_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "livestream_grpc_service_config.json",
    package = "google.cloud.video.livestream.v1",
    rest_numeric_enums = True,
    service_yaml = "livestream_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "video-livestream-v1-nodejs",
    deps = [
        ":livestream_nodejs_gapic",
        ":livestream_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "livestream_ruby_proto",
    deps = [":livestream_proto"],
)

ruby_grpc_library(
    name = "livestream_ruby_grpc",
    srcs = [":livestream_proto"],
    deps = [":livestream_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "livestream_ruby_gapic",
    srcs = [":livestream_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=livestream.googleapis.com",
        "ruby-cloud-api-shortname=livestream",
        "ruby-cloud-gem-name=google-cloud-video-live_stream-v1",
        "ruby-cloud-product-url=https://cloud.google.com/livestream/",
    ],
    grpc_service_config = "livestream_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The Live Stream API transcodes mezzanine live signals into direct-to-consumer streaming formats, including Dynamic Adaptive Streaming over HTTP (DASH/MPEG-DASH), and HTTP Live Streaming (HLS), for multiple device platforms.",
    ruby_cloud_title = "Live Stream V1",
    service_yaml = "livestream_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":livestream_ruby_grpc",
        ":livestream_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-video-livestream-v1-ruby",
    deps = [
        ":livestream_ruby_gapic",
        ":livestream_ruby_grpc",
        ":livestream_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "livestream_csharp_proto",
    extra_opts = [],
    deps = [":livestream_proto"],
)

csharp_grpc_library(
    name = "livestream_csharp_grpc",
    srcs = [":livestream_proto"],
    deps = [":livestream_csharp_proto"],
)

csharp_gapic_library(
    name = "livestream_csharp_gapic",
    srcs = [":livestream_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "livestream_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "livestream_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":livestream_csharp_grpc",
        ":livestream_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-video-livestream-v1-csharp",
    deps = [
        ":livestream_csharp_gapic",
        ":livestream_csharp_grpc",
        ":livestream_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "livestream_cc_proto",
    deps = [":livestream_proto"],
)

cc_grpc_library(
    name = "livestream_cc_grpc",
    srcs = [":livestream_proto"],
    grpc_only = True,
    deps = [":livestream_cc_proto"],
)
