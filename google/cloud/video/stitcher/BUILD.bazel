# This build file includes a target for the Ruby wrapper library for
# google-cloud-video-stitcher.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for videostitcher.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "stitcher_ruby_wrapper",
    srcs = ["//google/cloud/video/stitcher/v1:stitcher_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-video-stitcher",
        "ruby-cloud-wrapper-of=v1:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/video-stitcher/",
        "ruby-cloud-api-id=videostitcher.googleapis.com",
        "ruby-cloud-api-shortname=videostitcher",
    ],
    ruby_cloud_description = "The Video Stitcher API allows you to manipulate video content to dynamically insert ads prior to delivery to client devices. Using the Video Stitcher API, you can monetize your video-on-demand (VOD) and livestreaming videos by inserting ads as described by metadata stored on ad servers.",
    ruby_cloud_title = "Video Stitcher",
    transport = "grpc",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-video-stitcher-ruby",
    deps = [
        ":stitcher_ruby_wrapper",
    ],
)
