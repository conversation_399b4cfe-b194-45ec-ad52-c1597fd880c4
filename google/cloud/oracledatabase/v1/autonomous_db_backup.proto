// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.oracledatabase.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.OracleDatabase.V1";
option go_package = "cloud.google.com/go/oracledatabase/apiv1/oracledatabasepb;oracledatabasepb";
option java_multiple_files = true;
option java_outer_classname = "AutonomousDbBackupProto";
option java_package = "com.google.cloud.oracledatabase.v1";
option php_namespace = "Google\\Cloud\\OracleDatabase\\V1";
option ruby_package = "Google::Cloud::OracleDatabase::V1";

// Details of the Autonomous Database Backup resource.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/AutonomousDatabaseBackup/
message AutonomousDatabaseBackup {
  option (google.api.resource) = {
    type: "oracledatabase.googleapis.com/AutonomousDatabaseBackup"
    pattern: "projects/{project}/locations/{location}/autonomousDatabaseBackups/{autonomous_database_backup}"
    plural: "autonomousDatabaseBackups"
    singular: "autonomousDatabaseBackup"
  };

  // Identifier. The name of the Autonomous Database Backup resource with the
  // format:
  // projects/{project}/locations/{region}/autonomousDatabaseBackups/{autonomous_database_backup}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The name of the Autonomous Database resource for which the backup
  // is being created. Format:
  // projects/{project}/locations/{region}/autonomousDatabases/{autonomous_database}
  string autonomous_database = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "oracledatabase.googleapis.com/AutonomousDatabase"
    }
  ];

  // Optional. User friendly name for the Backup. The name does not have to be
  // unique.
  string display_name = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Various properties of the backup.
  AutonomousDatabaseBackupProperties properties = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. labels or tags associated with the resource.
  map<string, string> labels = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Properties of the Autonomous Database Backup resource.
message AutonomousDatabaseBackupProperties {
  // // The various lifecycle states of the Autonomous Database Backup.
  enum State {
    // Default unspecified value.
    STATE_UNSPECIFIED = 0;

    // Indicates that the resource is in creating state.
    CREATING = 1;

    // Indicates that the resource is in active state.
    ACTIVE = 2;

    // Indicates that the resource is in deleting state.
    DELETING = 3;

    // Indicates that the resource is in deleted state.
    DELETED = 4;

    // Indicates that the resource is in failed state.
    FAILED = 6;

    // Indicates that the resource is in updating state.
    UPDATING = 7;
  }

  // The type of the backup.
  enum Type {
    // Default unspecified value.
    TYPE_UNSPECIFIED = 0;

    // Incremental backups.
    INCREMENTAL = 1;

    // Full backups.
    FULL = 2;

    // Long term backups.
    LONG_TERM = 3;
  }

  // Output only. OCID of the Autonomous Database backup.
  // https://docs.oracle.com/en-us/iaas/Content/General/Concepts/identifiers.htm#Oracle
  string ocid = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Retention period in days for the backup.
  int32 retention_period_days = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The OCID of the compartment.
  string compartment_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The quantity of data in the database, in terabytes.
  float database_size_tb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A valid Oracle Database version for Autonomous Database.
  string db_version = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates if the backup is long term backup.
  bool is_long_term_backup = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates if the backup is automatic or user initiated.
  bool is_automatic_backup = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates if the backup can be used to restore the Autonomous
  // Database.
  bool is_restorable = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The OCID of the key store of Oracle Vault.
  string key_store_id = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The wallet name for Oracle Key Vault.
  string key_store_wallet = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The OCID of the key container that is used as the master
  // encryption key in database transparent data encryption (TDE) operations.
  string kms_key_id = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The OCID of the key container version that is used in database
  // transparent data encryption (TDE) operations KMS Key can have multiple key
  // versions. If none is specified, the current key version (latest) of the Key
  // Id is used for the operation. Autonomous Database Serverless does not use
  // key versions, hence is not applicable for Autonomous Database Serverless
  // instances.
  string kms_key_version_id = 12 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Additional information about the current lifecycle state.
  string lifecycle_details = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The lifecycle state of the backup.
  State lifecycle_state = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The backup size in terabytes.
  float size_tb = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp until when the backup will be available.
  google.protobuf.Timestamp available_till_time = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The date and time the backup completed.
  google.protobuf.Timestamp end_time = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The date and time the backup started.
  google.protobuf.Timestamp start_time = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the backup.
  Type type = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The OCID of the vault.
  string vault_id = 20 [(google.api.field_behavior) = OPTIONAL];
}
