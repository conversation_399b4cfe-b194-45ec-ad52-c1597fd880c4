// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.metastore.logging.v1;

option go_package = "cloud.google.com/go/metastore/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LogStreamsProto";
option java_package = "google.cloud.metastore.logging.v1";

// Stackdriver structured-payload for events generated from Hive Metastore
// API requests.
message RequestsLogEntry {
  // A free-text string describing the request.
  string message = 1;
}

// Stackdriver structured-payload for events generated from Hive Metastore
// system activity.
message SystemActivityLogEntry {
  // A free-text string describing the system activity.
  string message = 1;
}
