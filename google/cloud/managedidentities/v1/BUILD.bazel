# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/gapic-generator/tree/master/rules_gapic/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "managedidentities_proto",
    srcs = [
        "managed_identities_service.proto",
        "resource.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "managedidentities_proto_with_info",
    deps = [
        ":managedidentities_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "managedidentities_java_proto",
    deps = [":managedidentities_proto"],
)

java_grpc_library(
    name = "managedidentities_java_grpc",
    srcs = [":managedidentities_proto"],
    deps = [":managedidentities_java_proto"],
)

java_gapic_library(
    name = "managedidentities_java_gapic",
    srcs = [":managedidentities_proto_with_info"],
    grpc_service_config = "managedidentities_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "managedidentities_v1.yaml",
    test_deps = [
        ":managedidentities_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":managedidentities_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "managedidentities_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.managedidentities.v1.ManagedIdentitiesServiceClientTest",
    ],
    runtime_deps = [":managedidentities_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-managedidentities-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":managedidentities_java_gapic",
        ":managedidentities_java_grpc",
        ":managedidentities_java_proto",
        ":managedidentities_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "managedidentities_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/managedidentities/apiv1/managedidentitiespb",
    protos = [":managedidentities_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "managedidentities_go_gapic",
    srcs = [":managedidentities_proto_with_info"],
    grpc_service_config = "managedidentities_grpc_service_config.json",
    importpath = "cloud.google.com/go/managedidentities/apiv1;managedidentities",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "managedidentities_v1.yaml",
    transport = "grpc",
    deps = [
        ":managedidentities_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-managedidentities-v1-go",
    deps = [
        ":managedidentities_go_gapic",
        ":managedidentities_go_gapic_srcjar-snippets.srcjar",
        ":managedidentities_go_gapic_srcjar-test.srcjar",
        ":managedidentities_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "managedidentities_py_gapic",
    srcs = [":managedidentities_proto"],
    grpc_service_config = "managedidentities_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-managed-identities"],
    rest_numeric_enums = True,
    service_yaml = "managedidentities_v1.yaml",
    transport = "grpc",
)

py_test(
    name = "managedidentities_py_gapic_test",
    srcs = [
        "managedidentities_py_gapic_pytest.py",
        "managedidentities_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":managedidentities_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "managedidentities-v1-py",
    deps = [
        ":managedidentities_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "managedidentities_php_proto",
    deps = [":managedidentities_proto"],
)

php_gapic_library(
    name = "managedidentities_php_gapic",
    srcs = [":managedidentities_proto_with_info"],
    grpc_service_config = "managedidentities_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "managedidentities_v1.yaml",
    transport = "grpc+rest",
    deps = [":managedidentities_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-managedidentities-v1-php",
    deps = [
        ":managedidentities_php_gapic",
        ":managedidentities_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "managedidentities_nodejs_gapic",
    package_name = "@google-cloud/managed-identities",
    src = ":managedidentities_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "managedidentities_grpc_service_config.json",
    package = "google.cloud.managedidentities.v1",
    rest_numeric_enums = True,
    service_yaml = "managedidentities_v1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "managedidentities-v1-nodejs",
    deps = [
        ":managedidentities_nodejs_gapic",
        ":managedidentities_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "managedidentities_ruby_proto",
    deps = [":managedidentities_proto"],
)

ruby_grpc_library(
    name = "managedidentities_ruby_grpc",
    srcs = [":managedidentities_proto"],
    deps = [":managedidentities_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "managedidentities_ruby_gapic",
    srcs = [":managedidentities_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-managed_identities-v1",
        "ruby-cloud-env-prefix=MANAGED_IDENTITIES",
        "ruby-cloud-product-url=https://cloud.google.com/managed-microsoft-ad/",
        "ruby-cloud-api-id=managedidentities.googleapis.com",
        "ruby-cloud-api-shortname=managedidentities",
    ],
    grpc_service_config = "managedidentities_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The Managed Service for Microsoft Active Directory API is used for managing a highly available, hardened service running Microsoft Active Directory.",
    ruby_cloud_title = "Managed Service for Microsoft Active Directory API V1",
    service_yaml = "managedidentities_v1.yaml",
    transport = "grpc",
    deps = [
        ":managedidentities_ruby_grpc",
        ":managedidentities_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-managedidentities-v1-ruby",
    deps = [
        ":managedidentities_ruby_gapic",
        ":managedidentities_ruby_grpc",
        ":managedidentities_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "managedidentities_csharp_proto",
    deps = [":managedidentities_proto"],
)

csharp_grpc_library(
    name = "managedidentities_csharp_grpc",
    srcs = [":managedidentities_proto"],
    deps = [":managedidentities_csharp_proto"],
)

csharp_gapic_library(
    name = "managedidentities_csharp_gapic",
    srcs = [":managedidentities_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "managedidentities_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "managedidentities_v1.yaml",
    transport = "grpc",
    deps = [
        ":managedidentities_csharp_grpc",
        ":managedidentities_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-managedidentities-v1-csharp",
    deps = [
        ":managedidentities_csharp_gapic",
        ":managedidentities_csharp_grpc",
        ":managedidentities_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "managedidentities_cc_proto",
    deps = [":managedidentities_proto"],
)

cc_grpc_library(
    name = "managedidentities_cc_grpc",
    srcs = [":managedidentities_proto"],
    grpc_only = True,
    deps = [":managedidentities_cc_proto"],
)
