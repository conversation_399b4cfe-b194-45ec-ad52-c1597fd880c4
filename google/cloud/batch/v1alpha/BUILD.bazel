# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "batch_proto",
    srcs = [
        "batch.proto",
        "job.proto",
        "notification.proto",
        "resource_allowance.proto",
        "task.proto",
        "volume.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:interval_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "batch_proto_with_info",
    deps = [
        ":batch_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "batch_java_proto",
    deps = [":batch_proto"],
)

java_grpc_library(
    name = "batch_java_grpc",
    srcs = [":batch_proto"],
    deps = [":batch_java_proto"],
)

java_gapic_library(
    name = "batch_java_gapic",
    srcs = [":batch_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "batch_v1alpha_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "batch_v1alpha.yaml",
    test_deps = [
        ":batch_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":batch_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "batch_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.batch.v1alpha.BatchServiceClientHttpJsonTest",
        "com.google.cloud.batch.v1alpha.BatchServiceClientTest",
    ],
    runtime_deps = [":batch_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-batch-v1alpha-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":batch_java_gapic",
        ":batch_java_grpc",
        ":batch_java_proto",
        ":batch_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "batch_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/batch/apiv1alpha/batchpb",
    protos = [":batch_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:interval_go_proto",
    ],
)

go_gapic_library(
    name = "batch_go_gapic",
    srcs = [":batch_proto_with_info"],
    grpc_service_config = "batch_v1alpha_grpc_service_config.json",
    importpath = "cloud.google.com/go/batch/apiv1alpha;batch",
    metadata = True,
    release_level = "alpha",
    rest_numeric_enums = True,
    service_yaml = "batch_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":batch_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-batch-v1alpha-go",
    deps = [
        ":batch_go_gapic",
        ":batch_go_gapic_srcjar-metadata.srcjar",
        ":batch_go_gapic_srcjar-snippets.srcjar",
        ":batch_go_gapic_srcjar-test.srcjar",
        ":batch_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "batch_py_gapic",
    srcs = [":batch_proto"],
    grpc_service_config = "batch_v1alpha_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "batch_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "batch_py_gapic_test",
    srcs = [
        "batch_py_gapic_pytest.py",
        "batch_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":batch_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "batch-v1alpha-py",
    deps = [
        ":batch_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "batch_php_proto",
    deps = [":batch_proto"],
)

php_gapic_library(
    name = "batch_php_gapic",
    srcs = [":batch_proto_with_info"],
    grpc_service_config = "batch_v1alpha_grpc_service_config.json",
    migration_mode = "MIGRATING",
    rest_numeric_enums = True,
    service_yaml = "batch_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":batch_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-batch-v1alpha-php",
    deps = [
        ":batch_php_gapic",
        ":batch_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "batch_nodejs_gapic",
    package_name = "@google-cloud/batch",
    src = ":batch_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "batch_v1alpha_grpc_service_config.json",
    package = "google.cloud.batch.v1alpha",
    rest_numeric_enums = True,
    service_yaml = "batch_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "batch-v1alpha-nodejs",
    deps = [
        ":batch_nodejs_gapic",
        ":batch_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "batch_ruby_proto",
    deps = [":batch_proto"],
)

ruby_grpc_library(
    name = "batch_ruby_grpc",
    srcs = [":batch_proto"],
    deps = [":batch_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "batch_ruby_gapic",
    srcs = [":batch_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=batch.googleapis.com",
        "ruby-cloud-api-shortname=batch",
        "ruby-cloud-gem-name=google-cloud-batch-v1alpha",
    ],
    grpc_service_config = "batch_v1alpha_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Google Cloud Batch is a fully managed service used by scientists, VFX artists, developers to easily and efficiently run batch workloads on Google Cloud. This service manages provisioning of resources to satisfy the requirements of the batch jobs for a variety of workloads including ML, HPC, VFX rendering, transcoding, genomics and others.",
    ruby_cloud_title = "Batch V1alpha",
    service_yaml = "batch_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":batch_ruby_grpc",
        ":batch_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-batch-v1alpha-ruby",
    deps = [
        ":batch_ruby_gapic",
        ":batch_ruby_grpc",
        ":batch_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "batch_csharp_proto",
    deps = [":batch_proto"],
)

csharp_grpc_library(
    name = "batch_csharp_grpc",
    srcs = [":batch_proto"],
    deps = [":batch_csharp_proto"],
)

csharp_gapic_library(
    name = "batch_csharp_gapic",
    srcs = [":batch_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "batch_v1alpha_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "batch_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":batch_csharp_grpc",
        ":batch_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-batch-v1alpha-csharp",
    deps = [
        ":batch_csharp_gapic",
        ":batch_csharp_grpc",
        ":batch_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "batch_cc_proto",
    deps = [":batch_proto"],
)

cc_grpc_library(
    name = "batch_cc_grpc",
    srcs = [":batch_proto"],
    grpc_only = True,
    deps = [":batch_cc_proto"],
)
