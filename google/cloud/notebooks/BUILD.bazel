# This build file includes a target for the Ruby wrapper library for
# google-cloud-notebooks.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for notebooks.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1beta1 in this case.
ruby_cloud_gapic_library(
    name = "notebooks_ruby_wrapper",
    srcs = ["//google/cloud/notebooks/v1beta1:notebooks_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-notebooks",
        "ruby-cloud-env-prefix=NOTEBOOKS",
        "ruby-cloud-wrapper-of=v1:0.8;v1beta1:0.9",
        "ruby-cloud-product-url=https://cloud.google.com/ai-platform-notebooks",
        "ruby-cloud-api-id=notebooks.googleapis.com",
        "ruby-cloud-api-shortname=notebooks",
    ],
    ruby_cloud_description = "AI Platform Notebooks makes it easy to manage JupyterLab instances through a protected, publicly available notebook instance URL. A JupyterLab instance is a Deep Learning virtual machine instance with the latest machine learning and data science libraries pre-installed.",
    ruby_cloud_title = "AI Platform Notebooks",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-notebooks-ruby",
    deps = [
        ":notebooks_ruby_wrapper",
    ],
)
