// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.osconfig.v1;

option csharp_namespace = "Google.Cloud.OsConfig.V1";
option go_package = "cloud.google.com/go/osconfig/apiv1/osconfigpb;osconfigpb";
option java_outer_classname = "Common";
option java_package = "com.google.cloud.osconfig.v1";
option php_namespace = "Google\\Cloud\\OsConfig\\V1";
option ruby_package = "Google::Cloud::OsConfig::V1";

// Message encapsulating a value that can be either absolute ("fixed") or
// relative ("percent") to a value.
message FixedOrPercent {
  // Type of the value.
  oneof mode {
    // Specifies a fixed value.
    int32 fixed = 1;

    // Specifies the relative value defined as a percentage, which will be
    // multiplied by a reference value.
    int32 percent = 2;
  }
}
