type: google.api.Service
config_version: 3
name: recommendationengine.googleapis.com
title: Recommendations AI

apis:
- name: google.cloud.recommendationengine.v1beta1.CatalogService
- name: google.cloud.recommendationengine.v1beta1.PredictionApiKeyRegistry
- name: google.cloud.recommendationengine.v1beta1.PredictionService
- name: google.cloud.recommendationengine.v1beta1.UserEventService

types:
- name: google.cloud.recommendationengine.v1beta1.ImportCatalogItemsResponse
- name: google.cloud.recommendationengine.v1beta1.ImportErrorsConfig
- name: google.cloud.recommendationengine.v1beta1.ImportMetadata
- name: google.cloud.recommendationengine.v1beta1.ImportUserEventsResponse
- name: google.cloud.recommendationengine.v1beta1.PurgeUserEventsMetadata
- name: google.cloud.recommendationengine.v1beta1.PurgeUserEventsResponse

documentation:
  summary: |-
    Recommendations AI service enables customers to build end-to-end
    personalized recommendation systems without requiring a high level of
    expertise in machine learning, recommendation system, or Google Cloud.

backend:
  rules:
  - selector: google.cloud.recommendationengine.v1beta1.CatalogService.ImportCatalogItems
    deadline: 300.0
  - selector: google.cloud.recommendationengine.v1beta1.CatalogService.ListCatalogItems
    deadline: 300.0
  - selector: google.cloud.recommendationengine.v1beta1.UserEventService.ImportUserEvents
    deadline: 300.0
  - selector: google.cloud.recommendationengine.v1beta1.UserEventService.ListUserEvents
    deadline: 300.0

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1beta1/{name=projects/*/locations/*/catalogs/*/operations/*}'
    additional_bindings:
    - get: '/v1beta1/{name=projects/*/locations/*/catalogs/*/eventStores/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1beta1/{name=projects/*/locations/*/catalogs/*}/operations'
    additional_bindings:
    - get: '/v1beta1/{name=projects/*/locations/*/catalogs/*/eventStores/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.recommendationengine.v1beta1.CatalogService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.recommendationengine.v1beta1.PredictionApiKeyRegistry.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.recommendationengine.v1beta1.PredictionService.Predict
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.recommendationengine.v1beta1.UserEventService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
