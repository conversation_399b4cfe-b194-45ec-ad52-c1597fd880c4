// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "DeploymentProto";
option java_package = "com.google.cloud.dialogflow.cx.v3";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3";

// Service for managing [Deployments][google.cloud.dialogflow.cx.v3.Deployment].
service Deployments {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of all deployments in the specified
  // [Environment][google.cloud.dialogflow.cx.v3.Environment].
  rpc ListDeployments(ListDeploymentsRequest)
      returns (ListDeploymentsResponse) {
    option (google.api.http) = {
      get: "/v3/{parent=projects/*/locations/*/agents/*/environments/*}/deployments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified
  // [Deployment][google.cloud.dialogflow.cx.v3.Deployment].
  rpc GetDeployment(GetDeploymentRequest) returns (Deployment) {
    option (google.api.http) = {
      get: "/v3/{name=projects/*/locations/*/agents/*/environments/*/deployments/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Represents a deployment in an environment. A deployment happens when a flow
// version configured to be active in the environment. You can configure running
// pre-deployment steps, e.g. running validation test cases, experiment
// auto-rollout, etc.
message Deployment {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Deployment"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/environments/{environment}/deployments/{deployment}"
  };

  // The state of the deployment.
  enum State {
    // State unspecified.
    STATE_UNSPECIFIED = 0;

    // The deployment is running.
    RUNNING = 1;

    // The deployment succeeded.
    SUCCEEDED = 2;

    // The deployment failed.
    FAILED = 3;
  }

  // Result of the deployment.
  message Result {
    // Results of test cases running before the deployment.
    // Format:
    // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/testCases/<TestCaseID>/results/<TestCaseResultID>`.
    repeated string deployment_test_results = 1
        [(google.api.resource_reference) = {
          type: "dialogflow.googleapis.com/TestCaseResult"
        }];

    // The name of the experiment triggered by this deployment.
    // Format:
    // projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>/experiments/<ExperimentID>.
    string experiment = 2 [(google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Experiment"
    }];
  }

  // The name of the deployment.
  // Format:
  // projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>/deployments/<DeploymentID>.
  string name = 1;

  // The name of the flow version for this deployment.
  // Format:
  // projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/versions/<VerionID>.
  string flow_version = 2 [(google.api.resource_reference) = {
    type: "dialogflow.googleapis.com/Version"
  }];

  // The current state of the deployment.
  State state = 3;

  // Result of the deployment.
  Result result = 4;

  // Start time of this deployment.
  google.protobuf.Timestamp start_time = 5;

  // End time of this deployment.
  google.protobuf.Timestamp end_time = 6;
}

// The request message for
// [Deployments.ListDeployments][google.cloud.dialogflow.cx.v3.Deployments.ListDeployments].
message ListDeploymentsRequest {
  // Required. The [Environment][google.cloud.dialogflow.cx.v3.Environment] to
  // list all environments for. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Deployment"
    }
  ];

  // The maximum number of items to return in a single page. By default 20 and
  // at most 100.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;
}

// The response message for
// [Deployments.ListDeployments][google.cloud.dialogflow.cx.v3.Deployments.ListDeployments].
message ListDeploymentsResponse {
  // The list of deployments. There will be a maximum number of items
  // returned based on the page_size field in the request. The list may in some
  // cases be empty or contain fewer entries than page_size even if this isn't
  // the last page.
  repeated Deployment deployments = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for
// [Deployments.GetDeployment][google.cloud.dialogflow.cx.v3.Deployments.GetDeployment].
message GetDeploymentRequest {
  // Required. The name of the
  // [Deployment][google.cloud.dialogflow.cx.v3.Deployment]. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>/deployments/<DeploymentID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Deployment"
    }
  ];
}
