{"methodConfig": [{"name": [{"service": "google.cloud.dialogflow.cx.v3.Agents"}, {"service": "google.cloud.dialogflow.cx.v3.Changelogs"}, {"service": "google.cloud.dialogflow.cx.v3.Deployments"}, {"service": "google.cloud.dialogflow.cx.v3.EncryptionSpecService"}, {"service": "google.cloud.dialogflow.cx.v3.EntityTypes"}, {"service": "google.cloud.dialogflow.cx.v3.Environments"}, {"service": "google.cloud.dialogflow.cx.v3.Experiments"}, {"service": "google.cloud.dialogflow.cx.v3.Flows"}, {"service": "google.cloud.dialogflow.cx.v3.Fulfillments"}, {"service": "google.cloud.dialogflow.cx.v3.Generators"}, {"service": "google.cloud.dialogflow.cx.v3.Intents"}, {"service": "google.cloud.dialogflow.cx.v3.Pages"}, {"service": "google.cloud.dialogflow.cx.v3.SecuritySettingsService"}, {"service": "google.cloud.dialogflow.cx.v3.Sessions"}, {"service": "google.cloud.dialogflow.cx.v3.SessionEntityTypes"}, {"service": "google.cloud.dialogflow.cx.v3.TestCases"}, {"service": "google.cloud.dialogflow.cx.v3.TransitionRouteGroups"}, {"service": "google.cloud.dialogflow.cx.v3.Versions"}, {"service": "google.cloud.dialogflow.cx.v3.Webhooks"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dialogflow.cx.v3.Agents", "method": "CreateAgent"}], "timeout": "180s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dialogflow.cx.v3.Sessions", "method": "DetectIntent"}], "timeout": "220s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dialogflow.cx.v3.Sessions", "method": "StreamingDetectIntent"}, {"service": "google.cloud.dialogflow.cx.v3.Sessions", "method": "ServerStreamingDetectIntent"}], "timeout": "220s"}]}