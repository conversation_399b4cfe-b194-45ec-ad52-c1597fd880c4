// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1beta/common.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Beta";
option go_package = "cloud.google.com/go/discoveryengine/apiv1beta/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "ControlProto";
option java_package = "com.google.cloud.discoveryengine.v1beta";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1beta";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1beta";

// Defines circumstances to be checked before allowing a behavior
message Condition {
  // Matcher for search request query
  message QueryTerm {
    // The specific query value to match against
    //
    // Must be lowercase, must be UTF-8.
    // Can have at most 3 space separated terms if full_match is true.
    // Cannot be an empty string.
    // Maximum length of 5000 characters.
    string value = 1;

    // Whether the search query needs to exactly match the query term.
    bool full_match = 2;
  }

  // Used for time-dependent conditions.
  message TimeRange {
    // Start of time range.
    //
    // Range is inclusive.
    google.protobuf.Timestamp start_time = 1;

    // End of time range.
    //
    // Range is inclusive.
    // Must be in the future.
    google.protobuf.Timestamp end_time = 2;
  }

  // Search only
  // A list of terms to match the query on.
  // Cannot be set when
  // [Condition.query_regex][google.cloud.discoveryengine.v1beta.Condition.query_regex]
  // is set.
  //
  // Maximum of 10 query terms.
  repeated QueryTerm query_terms = 2;

  // Range of time(s) specifying when condition is active.
  //
  // Maximum of 10 time ranges.
  repeated TimeRange active_time_range = 3;

  // Optional. Query regex to match the whole search query.
  // Cannot be set when
  // [Condition.query_terms][google.cloud.discoveryengine.v1beta.Condition.query_terms]
  // is set. This is currently supporting promotion use case.
  string query_regex = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Defines a conditioned behavior to employ during serving.
// Must be attached to a
// [ServingConfig][google.cloud.discoveryengine.v1beta.ServingConfig] to be
// considered at serving time. Permitted actions dependent on `SolutionType`.
message Control {
  option (google.api.resource) = {
    type: "discoveryengine.googleapis.com/Control"
    pattern: "projects/{project}/locations/{location}/dataStores/{data_store}/controls/{control}"
    pattern: "projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/controls/{control}"
    pattern: "projects/{project}/locations/{location}/collections/{collection}/engines/{engine}/controls/{control}"
  };

  // Adjusts order of products in returned list.
  message BoostAction {
    // Required. Strength of the boost, which should be in [-1, 1]. Negative
    // boost means demotion. Default is 0.0 (No-op).
    float boost = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Specifies which products to apply the boost to.
    //
    // If no filter is provided all products will be boosted (No-op).
    // Syntax documentation:
    // https://cloud.google.com/retail/docs/filter-and-order
    // Maximum length is 5000 characters.
    // Otherwise an INVALID ARGUMENT error is thrown.
    string filter = 2 [(google.api.field_behavior) = REQUIRED];

    // Required. Specifies which data store's documents can be boosted by this
    // control. Full data store name e.g.
    // projects/123/locations/global/collections/default_collection/dataStores/default_data_store
    string data_store = 3 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "discoveryengine.googleapis.com/DataStore"
      }
    ];
  }

  // Specified which products may be included in results.
  // Uses same filter as boost.
  message FilterAction {
    // Required. A filter to apply on the matching condition results.
    //
    // Required
    // Syntax documentation:
    // https://cloud.google.com/retail/docs/filter-and-order
    // Maximum length is 5000 characters. Otherwise an INVALID
    // ARGUMENT error is thrown.
    string filter = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Specifies which data store's documents can be filtered by this
    // control. Full data store name e.g.
    // projects/123/locations/global/collections/default_collection/dataStores/default_data_store
    string data_store = 2 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "discoveryengine.googleapis.com/DataStore"
      }
    ];
  }

  // Redirects a shopper to the provided URI.
  message RedirectAction {
    // Required. The URI to which the shopper will be redirected.
    //
    // Required.
    // URI must have length equal or less than 2000 characters.
    // Otherwise an INVALID ARGUMENT error is thrown.
    string redirect_uri = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Creates a set of terms that will act as synonyms of one another.
  //
  // Example: "happy" will also be considered as "glad", "glad" will also be
  // considered as "happy".
  message SynonymsAction {
    // Defines a set of synonyms.
    // Can specify up to 100 synonyms.
    // Must specify at least 2 synonyms. Otherwise an INVALID ARGUMENT error is
    // thrown.
    repeated string synonyms = 1;
  }

  // Actions are restricted by Vertical and Solution
  //
  // Required.
  oneof action {
    // Defines a boost-type control
    BoostAction boost_action = 6;

    // Defines a filter-type control
    // Currently not supported by Recommendation
    FilterAction filter_action = 7;

    // Defines a redirect-type control.
    RedirectAction redirect_action = 9;

    // Treats a group of terms as synonyms of one another.
    SynonymsAction synonyms_action = 10;
  }

  // Immutable. Fully qualified name
  // `projects/*/locations/global/dataStore/*/controls/*`
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Required. Human readable name. The identifier used in UI views.
  //
  // Must be UTF-8 encoded string. Length limit is 128 characters.
  // Otherwise an INVALID ARGUMENT error is thrown.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. List of all
  // [ServingConfig][google.cloud.discoveryengine.v1beta.ServingConfig] IDs this
  // control is attached to. May take up to 10 minutes to update after changes.
  repeated string associated_serving_config_ids = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. What solution the control belongs to.
  //
  // Must be compatible with vertical of resource.
  // Otherwise an INVALID ARGUMENT error is thrown.
  SolutionType solution_type = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Specifies the use case for the control.
  // Affects what condition fields can be set.
  // Only applies to
  // [SOLUTION_TYPE_SEARCH][google.cloud.discoveryengine.v1beta.SolutionType.SOLUTION_TYPE_SEARCH].
  // Currently only allow one use case per control.
  // Must be set when solution_type is
  // [SolutionType.SOLUTION_TYPE_SEARCH][google.cloud.discoveryengine.v1beta.SolutionType.SOLUTION_TYPE_SEARCH].
  repeated SearchUseCase use_cases = 8;

  // Determines when the associated action will trigger.
  //
  // Omit to always apply the action.
  // Currently only a single condition may be specified.
  // Otherwise an INVALID ARGUMENT error is thrown.
  repeated Condition conditions = 5;
}
