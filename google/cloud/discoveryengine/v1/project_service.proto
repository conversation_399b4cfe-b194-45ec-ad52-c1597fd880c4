// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1/project.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1";
option go_package = "cloud.google.com/go/discoveryengine/apiv1/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "ProjectServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1";

// Service for operations on the
// [Project][google.cloud.discoveryengine.v1.Project].
service ProjectService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Provisions the project resource. During the
  // process, related systems will get prepared and initialized.
  //
  // Caller must read the [Terms for data
  // use](https://cloud.google.com/retail/data-use-terms), and optionally
  // specify in request to provide consent to that service terms.
  rpc ProvisionProject(ProvisionProjectRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*}:provision"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1.Project"
      metadata_type: "google.cloud.discoveryengine.v1.ProvisionProjectMetadata"
    };
  }
}

// Request for
// [ProjectService.ProvisionProject][google.cloud.discoveryengine.v1.ProjectService.ProvisionProject]
// method.
message ProvisionProjectRequest {
  // Required. Full resource name of a
  // [Project][google.cloud.discoveryengine.v1.Project], such as
  // `projects/{project_id_or_number}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Project"
    }
  ];

  // Required. Set to `true` to specify that caller has read and would like to
  // give consent to the [Terms for data
  // use](https://cloud.google.com/retail/data-use-terms).
  bool accept_data_use_terms = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The version of the [Terms for data
  // use](https://cloud.google.com/retail/data-use-terms) that caller has read
  // and would like to give consent to.
  //
  // Acceptable version is `2022-11-23`, and this may change over time.
  string data_use_terms_version = 3 [(google.api.field_behavior) = REQUIRED];
}

// Metadata associated with a project provision operation.
message ProvisionProjectMetadata {}
