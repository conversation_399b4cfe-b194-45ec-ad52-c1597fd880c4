type: google.api.Service
config_version: 3
name: securitycenter.googleapis.com
title: Cloud Security Command Center API

apis:
- name: google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService

documentation:
  summary: |-
    Cloud Security Command Center Settings API provides functionality to retrieve and update configurations.

backend:
  rules:
  - selector: 'google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService.*'
    deadline: 480.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0

authentication:
  rules:
  - selector: 'google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
