# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "language_proto",
    srcs = [
        "language_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
    ],
)

proto_library_with_info(
    name = "language_proto_with_info",
    deps = [
        ":language_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "language_java_proto",
    deps = [":language_proto"],
)

java_grpc_library(
    name = "language_java_grpc",
    srcs = [":language_proto"],
    deps = [":language_java_proto"],
)

java_gapic_library(
    name = "language_java_gapic",
    srcs = [":language_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "language_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "language_v1beta2.yaml",
    test_deps = [
        ":language_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":language_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "language_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.language.v1beta2.LanguageServiceClientHttpJsonTest",
        "com.google.cloud.language.v1beta2.LanguageServiceClientTest",
    ],
    runtime_deps = [":language_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-language-v1beta2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":language_java_gapic",
        ":language_java_grpc",
        ":language_java_proto",
        ":language_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "language_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/language/apiv1beta2/languagepb",
    protos = [":language_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "language_go_gapic",
    srcs = [":language_proto_with_info"],
    grpc_service_config = "language_grpc_service_config.json",
    importpath = "cloud.google.com/go/language/apiv1beta2;language",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "language_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [
        ":language_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-language-v1beta2-go",
    deps = [
        ":language_go_gapic",
        ":language_go_gapic_srcjar-metadata.srcjar",
        ":language_go_gapic_srcjar-snippets.srcjar",
        ":language_go_gapic_srcjar-test.srcjar",
        ":language_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "language_py_gapic",
    srcs = [":language_proto"],
    grpc_service_config = "language_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "language_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "language_py_gapic_test",
    srcs = [
        "language_py_gapic_pytest.py",
        "language_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":language_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "language-v1beta2-py",
    deps = [
        ":language_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "language_php_proto",
    deps = [":language_proto"],
)

php_gapic_library(
    name = "language_php_gapic",
    srcs = [":language_proto_with_info"],
    grpc_service_config = "language_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "language_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [":language_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-language-v1beta2-php",
    deps = [
        ":language_php_gapic",
        ":language_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "language_nodejs_gapic",
    package_name = "@google-cloud/language",
    src = ":language_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "language_grpc_service_config.json",
    package = "google.cloud.language.v1beta2",
    rest_numeric_enums = True,
    service_yaml = "language_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "language-v1beta2-nodejs",
    deps = [
        ":language_nodejs_gapic",
        ":language_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "language_ruby_proto",
    deps = [":language_proto"],
)

ruby_grpc_library(
    name = "language_ruby_grpc",
    srcs = [":language_proto"],
    deps = [":language_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "language_ruby_gapic",
    srcs = [":language_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=language.googleapis.com",
        "ruby-cloud-api-shortname=language",
        "ruby-cloud-env-prefix=LANGUAGE",
        "ruby-cloud-gem-name=google-cloud-language-v1beta2",
        "ruby-cloud-product-url=https://cloud.google.com/natural-language",
    ],
    grpc_service_config = "language_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Provides natural language understanding technologies, such as sentiment analysis, entity recognition, entity sentiment analysis, and other text annotations.",
    ruby_cloud_title = "Natural Language V1beta2",
    service_yaml = "language_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [
        ":language_ruby_grpc",
        ":language_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-language-v1beta2-ruby",
    deps = [
        ":language_ruby_gapic",
        ":language_ruby_grpc",
        ":language_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "language_csharp_proto",
    deps = [":language_proto"],
)

csharp_grpc_library(
    name = "language_csharp_grpc",
    srcs = [":language_proto"],
    deps = [":language_csharp_proto"],
)

csharp_gapic_library(
    name = "language_csharp_gapic",
    srcs = [":language_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "language_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "language_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [
        ":language_csharp_grpc",
        ":language_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-language-v1beta2-csharp",
    deps = [
        ":language_csharp_gapic",
        ":language_csharp_grpc",
        ":language_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "language_cc_proto",
    deps = [":language_proto"],
)

cc_grpc_library(
    name = "language_cc_grpc",
    srcs = [":language_proto"],
    grpc_only = True,
    deps = [":language_cc_proto"],
)
