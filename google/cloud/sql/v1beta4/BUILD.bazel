# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "sql_proto",
    srcs = [
        "cloud_sql.proto",
        "cloud_sql_connect.proto",
        "cloud_sql_iam_policies.proto",
        "cloud_sql_resources.proto",
        "cloud_sql_tiers.proto",
        "cloud_sql_users.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "sql_proto_with_info",
    deps = [
        ":sql_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
#load(
#    "@com_google_googleapis_imports//:imports.bzl",
#    "java_gapic_assembly_gradle_pkg",
#    "java_gapic_library",
#    "java_gapic_test",
#    "java_grpc_library",
#    "java_proto_library",
#)

#java_proto_library(
#    name = "sql_java_proto",
#    deps = [":sql_proto"],
#)

#java_grpc_library(
#    name = "sql_java_grpc",
#    srcs = [":sql_proto"],
#    deps = [":sql_java_proto"],
#)

#java_gapic_library(
#    name = "sql_java_gapic",
#    srcs = [":sql_proto_with_info"],
#    gapic_yaml = None,
#    grpc_service_config = "sqladmin_grpc_service_config.json",
#    service_yaml = "sqladmin_v1beta4.yaml",
#    test_deps = [
#        ":sql_java_grpc",
#    ],
#    deps = [
#        ":sql_java_proto",
#        "//google/api:api_java_proto",
#    ],
#)

#java_gapic_test(
#    name = "sql_java_gapic_test_suite",
#    test_classes = [
#        "com.google.cloud.sql.v1beta4.SqlBackupRunsServiceClientTest",
#        "com.google.cloud.sql.v1beta4.SqlConnectServiceClientTest",
#        "com.google.cloud.sql.v1beta4.SqlDatabasesServiceClientTest",
#        "com.google.cloud.sql.v1beta4.SqlFlagsServiceClientTest",
#        "com.google.cloud.sql.v1beta4.SqlInstancesServiceClientTest",
#        "com.google.cloud.sql.v1beta4.SqlOperationsServiceClientTest",
#        "com.google.cloud.sql.v1beta4.SqlSslCertsServiceClientTest",
#        "com.google.cloud.sql.v1beta4.SqlTiersServiceClientTest",
#        "com.google.cloud.sql.v1beta4.SqlUsersServiceClientTest",
#    ],
#    runtime_deps = [":sql_java_gapic_test"],
#)

# Open Source Packages
#java_gapic_assembly_gradle_pkg(
#    name = "google-cloud-sql-v1beta4-java",
#    deps = [
#        ":sql_java_gapic",
#        ":sql_java_grpc",
#        ":sql_java_proto",
#        ":sql_proto",
#    ],
#    include_samples = True,
#)

##############################################################################
# Go
##############################################################################
# load(
#    "@com_google_googleapis_imports//:imports.bzl",
#    "go_gapic_assembly_pkg",
#    "go_gapic_library",
#    "go_proto_library",
#)

#go_proto_library(
#    name = "sql_go_proto",
#    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
#    importpath = "cloud.google.com/go/sql/apiv1beta4/sqlpb",
#    protos = [":sql_proto"],
#    deps = [
#        "//google/api:annotations_go_proto",
#    ],
#)

#go_gapic_library(
#    name = "sql_go_gapic",
#    srcs = [":sql_proto_with_info"],
#    grpc_service_config = "sqladmin_grpc_service_config.json",
#    importpath = "cloud.google.com/go/sql/apiv1beta4;sql",
#    metadata = True,
#   release_level = "beta",
#    service_yaml = "sqladmin_v1beta4.yaml",
#    deps = [
#        ":sql_go_proto",
#        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
#    ],
#)

# Open Source Packages
#go_gapic_assembly_pkg(
#    name = "gapi-cloud-sql-v1beta4-go",
#    deps = [
#        ":sql_go_gapic",
#        ":sql_go_gapic_srcjar-metadata.srcjar",
#        ":sql_go_gapic_srcjar-snippets.srcjar",
#        ":sql_go_gapic_srcjar-test.srcjar",
#        ":sql_go_proto",
#    ],
#)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "sql_py_gapic",
    srcs = [":sql_proto"],
    grpc_service_config = "sqladmin_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "sqladmin_v1beta4.yaml",
    transport = "grpc",
    deps = [
    ],
)

py_test(
    name = "sql_py_gapic_test",
    srcs = [
        "sql_py_gapic_pytest.py",
        "sql_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":sql_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "sql-v1beta4-py",
    deps = [
        ":sql_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "sql_php_proto",
    deps = [":sql_proto"],
)

php_gapic_library(
    name = "sql_php_gapic",
    srcs = [":sql_proto_with_info"],
    grpc_service_config = "sqladmin_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "sqladmin_v1beta4.yaml",
    transport = "grpc+rest",
    deps = [
        ":sql_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-sql-v1beta4-php",
    deps = [
        ":sql_php_gapic",
        ":sql_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "sql_nodejs_gapic",
    package_name = "@google-cloud/sql",
    src = ":sql_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "sqladmin_grpc_service_config.json",
    package = "google.cloud.sql.v1beta4",
    rest_numeric_enums = True,
    service_yaml = "sqladmin_v1beta4.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "sql-v1beta4-nodejs",
    deps = [
        ":sql_nodejs_gapic",
        ":sql_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "sql_ruby_proto",
    deps = [":sql_proto"],
)

ruby_grpc_library(
    name = "sql_ruby_grpc",
    srcs = [":sql_proto"],
    deps = [":sql_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "sql_ruby_gapic",
    srcs = [":sql_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-sql-v1beta4"],
    grpc_service_config = "sqladmin_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "sqladmin_v1beta4.yaml",
    transport = "grpc+rest",
    deps = [
        ":sql_ruby_grpc",
        ":sql_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-sql-v1beta4-ruby",
    deps = [
        ":sql_ruby_gapic",
        ":sql_ruby_grpc",
        ":sql_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
#load(
#    "@com_google_googleapis_imports//:imports.bzl",
#    "csharp_gapic_assembly_pkg",
#    "csharp_gapic_library",
#    "csharp_grpc_library",
#    "csharp_proto_library",
#)

#csharp_proto_library(
#    name = "sql_csharp_proto",
#    deps = [":sql_proto"],
#)

#csharp_grpc_library(
#    name = "sql_csharp_grpc",
#    srcs = [":sql_proto"],
#    deps = [":sql_csharp_proto"],
#)

#csharp_gapic_library(
#    name = "sql_csharp_gapic",
#    srcs = [":sql_proto_with_info"],
#    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
#    grpc_service_config = "sqladmin_grpc_service_config.json",
#    service_yaml = "sqladmin_v1beta4.yaml",
#    deps = [
#        ":sql_csharp_grpc",
#        ":sql_csharp_proto",
#    ],
#)

# Open Source Packages
# csharp_gapic_assembly_pkg(
#    name = "google-cloud-sql-v1beta4-csharp",
#    deps = [
#        ":sql_csharp_gapic",
#        ":sql_csharp_grpc",
#        ":sql_csharp_proto",
#    ],
#)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "sql_cc_proto",
    deps = [":sql_proto"],
)

cc_grpc_library(
    name = "sql_cc_grpc",
    srcs = [":sql_proto"],
    grpc_only = True,
    deps = [":sql_cc_proto"],
)
