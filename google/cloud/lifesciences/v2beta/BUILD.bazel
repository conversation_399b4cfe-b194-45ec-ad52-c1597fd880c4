# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "lifesciences_proto",
    srcs = [
        "workflows.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:code_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "lifesciences_proto_with_info",
    deps = [
        ":lifesciences_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "lifesciences_java_proto",
    deps = [":lifesciences_proto"],
)

java_grpc_library(
    name = "lifesciences_java_grpc",
    srcs = [":lifesciences_proto"],
    deps = [":lifesciences_java_proto"],
)

java_gapic_library(
    name = "lifesciences_java_gapic",
    srcs = [":lifesciences_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "lifesciences_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "lifesciences_v2beta.yaml",
    test_deps = [
        ":lifesciences_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":lifesciences_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "lifesciences_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.lifesciences.v2beta.WorkflowsServiceV2BetaClientHttpJsonTest",
        "com.google.cloud.lifesciences.v2beta.WorkflowsServiceV2BetaClientTest",
    ],
    runtime_deps = [":lifesciences_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-lifesciences-v2beta-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":lifesciences_java_gapic",
        ":lifesciences_java_grpc",
        ":lifesciences_java_proto",
        ":lifesciences_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "lifesciences_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/lifesciences/apiv2beta/lifesciencespb",
    protos = [":lifesciences_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:code_go_proto",
    ],
)

go_gapic_library(
    name = "lifesciences_go_gapic",
    srcs = [":lifesciences_proto_with_info"],
    grpc_service_config = "lifesciences_grpc_service_config.json",
    importpath = "cloud.google.com/go/lifesciences/apiv2beta;lifesciences",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "lifesciences_v2beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":lifesciences_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-lifesciences-v2beta-go",
    deps = [
        ":lifesciences_go_gapic",
        ":lifesciences_go_gapic_srcjar-metadata.srcjar",
        ":lifesciences_go_gapic_srcjar-snippets.srcjar",
        ":lifesciences_go_gapic_srcjar-test.srcjar",
        ":lifesciences_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "lifesciences_py_gapic",
    srcs = [":lifesciences_proto"],
    grpc_service_config = "lifesciences_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-life-sciences"],
    rest_numeric_enums = True,
    service_yaml = "lifesciences_v2beta.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "lifesciences_py_gapic_test",
    srcs = [
        "lifesciences_py_gapic_pytest.py",
        "lifesciences_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":lifesciences_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "lifesciences-v2beta-py",
    deps = [
        ":lifesciences_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "lifesciences_php_proto",
    deps = [":lifesciences_proto"],
)

php_gapic_library(
    name = "lifesciences_php_gapic",
    srcs = [":lifesciences_proto_with_info"],
    grpc_service_config = "lifesciences_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "lifesciences_v2beta.yaml",
    transport = "grpc+rest",
    deps = [":lifesciences_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-lifesciences-v2beta-php",
    deps = [
        ":lifesciences_php_gapic",
        ":lifesciences_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "lifesciences_nodejs_gapic",
    package_name = "@google-cloud/life-sciences",
    src = ":lifesciences_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "lifesciences_grpc_service_config.json",
    package = "google.cloud.lifesciences.v2beta",
    rest_numeric_enums = True,
    service_yaml = "lifesciences_v2beta.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "lifesciences-v2beta-nodejs",
    deps = [
        ":lifesciences_nodejs_gapic",
        ":lifesciences_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "lifesciences_ruby_proto",
    deps = [":lifesciences_proto"],
)

ruby_grpc_library(
    name = "lifesciences_ruby_grpc",
    srcs = [":lifesciences_proto"],
    deps = [":lifesciences_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "lifesciences_ruby_gapic",
    srcs = [":lifesciences_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=lifesciences.googleapis.com",
        "ruby-cloud-api-shortname=lifesciences",
        "ruby-cloud-env-prefix=LIFE_SCIENCES",
        "ruby-cloud-gem-name=google-cloud-life_sciences-v2beta",
        "ruby-cloud-product-url=https://cloud.google.com/life-sciences/",
        "ruby-cloud-service-override=WorkflowsServiceV2Beta=WorkflowsService",
    ],
    grpc_service_config = "lifesciences_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Life Sciences is a suite of services and tools for managing, processing, and transforming life sciences data. It also enables advanced insights and operational workflows using highly scalable and compliant infrastructure.",
    ruby_cloud_title = "Cloud Life Sciences V2beta",
    service_yaml = "lifesciences_v2beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":lifesciences_ruby_grpc",
        ":lifesciences_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-lifesciences-v2beta-ruby",
    deps = [
        ":lifesciences_ruby_gapic",
        ":lifesciences_ruby_grpc",
        ":lifesciences_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "lifesciences_csharp_proto",
    deps = [":lifesciences_proto"],
)

csharp_grpc_library(
    name = "lifesciences_csharp_grpc",
    srcs = [":lifesciences_proto"],
    deps = [":lifesciences_csharp_proto"],
)

csharp_gapic_library(
    name = "lifesciences_csharp_gapic",
    srcs = [":lifesciences_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "lifesciences_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "lifesciences_v2beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":lifesciences_csharp_grpc",
        ":lifesciences_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-lifesciences-v2beta-csharp",
    deps = [
        ":lifesciences_csharp_gapic",
        ":lifesciences_csharp_grpc",
        ":lifesciences_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "lifesciences_cc_proto",
    deps = [":lifesciences_proto"],
)

cc_grpc_library(
    name = "lifesciences_cc_grpc",
    srcs = [":lifesciences_proto"],
    grpc_only = True,
    deps = [":lifesciences_cc_proto"],
)
