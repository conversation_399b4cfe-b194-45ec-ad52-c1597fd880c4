// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.dashboard.v1;

import "google/api/field_behavior.proto";
import "google/monitoring/dashboard/v1/metrics.proto";
import "google/protobuf/duration.proto";

option csharp_namespace = "Google.Cloud.Monitoring.Dashboard.V1";
option go_package = "cloud.google.com/go/monitoring/dashboard/apiv1/dashboardpb;dashboardpb";
option java_multiple_files = true;
option java_outer_classname = "Pie<PERSON>hartProto";
option java_package = "com.google.monitoring.dashboard.v1";
option php_namespace = "Google\\Cloud\\Monitoring\\Dashboard\\V1";
option ruby_package = "Google::Cloud::Monitoring::Dashboard::V1";

// A widget that displays timeseries data as a pie or a donut.
message PieChart {
  // Groups a time series query definition.
  message PieChartDataSet {
    // Required. The query for the PieChart. See,
    // `google.monitoring.dashboard.v1.TimeSeriesQuery`.
    TimeSeriesQuery time_series_query = 1
        [(google.api.field_behavior) = REQUIRED];

    // Optional. A template for the name of the slice. This name will be
    // displayed in the legend and the tooltip of the pie chart. It replaces the
    // auto-generated names for the slices. For example, if the template is set
    // to
    // `${resource.labels.zone}`, the zone's value will be used for the name
    // instead of the default name.
    string slice_name_template = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The lower bound on data point frequency for this data set,
    // implemented by specifying the minimum alignment period to use in a time
    // series query. For example, if the data is published once every 10
    // minutes, the `min_alignment_period` should be at least 10 minutes. It
    // would not make sense to fetch and align data at one minute intervals.
    google.protobuf.Duration min_alignment_period = 3
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Types for the pie chart.
  enum PieChartType {
    // The zero value. No type specified. Do not use.
    PIE_CHART_TYPE_UNSPECIFIED = 0;

    // A Pie type PieChart.
    PIE = 1;

    // Similar to PIE, but the DONUT type PieChart has a hole in the middle.
    DONUT = 2;
  }

  // Required. The queries for the chart's data.
  repeated PieChartDataSet data_sets = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Indicates the visualization type for the PieChart.
  PieChartType chart_type = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Indicates whether or not the pie chart should show slices' labels
  bool show_labels = 4 [(google.api.field_behavior) = OPTIONAL];
}
