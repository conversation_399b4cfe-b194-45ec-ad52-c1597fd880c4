package apitesttask

import (
	zeroutils "github.com/zeromicro/go-zero/core/utils"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func mock_pool_account_execution_data_from_json(componentData string) *managerpb.ApiExecutionData {
	component := &managerpb.PoolAccountComponent{}
	if err := managerpb.DefaultUnmarshalOptions.Unmarshal(zeroutils.StringToByteSlice(componentData), component); err != nil {
		panic(any("无效的account组件信息, err:" + err.Error()))
	}

	execData := &managerpb.ApiExecutionData{
		Id:   utils.GenNanoId("POOL_ACCOUNT_"),
		Type: managerpb.ApiExecutionDataType_POOL_ACCOUNT,
		Data: &managerpb.ApiExecutionData_Account{
			Account: component,
		},
	}

	return execData
}
