package apitesttask

import (
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type WaitNode struct {
	*BaseNode
	source *managerpb.WaitComponent
	logW   *WaitNodeLogWriter
}

func NewWaitNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (Node, error) {
	node := &WaitNode{}
	var err error
	node.BaseNode, err = NewBaseNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	node.source = data.GetWait()
	node.logW = NewWaitNodeLogWriter(node.BaseNode.logW)

	return node, nil
}

func (node *WaitNode) run() (err error) {
	begin := time.Now().UnixMilli()
	defer func() {
		end := time.Now().UnixMilli()
		node.logW.SetCostMs(end - begin)
	}()

	switch node.source.GetType() {
	case managerpb.WaitComponent_SLEEP:
		node.runWaitSleep(node.source.GetSleep())
	case managerpb.WaitComponent_MANUAL:
		node.runWaitManual(node.source.GetManual())
	}

	return nil
}

func (node *WaitNode) runWaitSleep(sleep *managerpb.WaitComponent_Sleep) {
	ms := sleep.GetTime()
	time.Sleep(time.Duration(ms) * time.Millisecond)
}

func (node *WaitNode) runWaitManual(manual *managerpb.WaitComponent_Manual) {
	// TODO: 未实现
}

func (node *WaitNode) Content() string {
	return node.logW.toJson()
}

func (node *WaitNode) Logger() NodeLogger {
	return node.logW
}
