package clickPilot

import "time"

const (
	optimizeTaskStepsAPIName = "/api/v1/ui-task/steps/optimize"
	createUITaskAPIName      = "/api/v1/ui-task/create"
	getTaskStatusAPIName     = "/api/v1/ui-task/status/get"
	getTaskRecordAPIName     = "/api/v1/ui-task/record/get"
	getTaskLogAPIName        = "/api/v1/ui-task/log/get"
	stopTaskAPIName          = "/api/v1/ui-task/stop"
	deleteTaskAPIName        = "/api/v1/ui-task/delete"
	executeRefTaskAPIName    = "/api/v1/reference-task/execute"
	createRefCaseAPIName     = "/api/v1/reference-task/copy-as-success-case"
	deleteRefCaseAPIName     = "/api/v1/reference-task/success-case"
	deleteRefCaseStepAPIName = "/api/v1/reference-task/success-case-action"

	queryOfTaskID        = "task_id"
	defaultAgentConfigID = "tt"

	requestTimeout = 5 * time.Second
)

// ExecutionMode 执行模式
type ExecutionMode string

const (
	ExecutionModeOfAgent ExecutionMode = "aggregation"  // Agent模式
	ExecutionModeOfStep  ExecutionMode = "step_by_step" // Step模式
)

// AgentType 代理类型
type AgentType string

const (
	AgentTypeOfAndroid AgentType = "android"
	AgentTypeOfIOS     AgentType = "ios"
)

// DeviceType 设备类型
type DeviceType string

const (
	DeviceTypeOfAndroid DeviceType = "android"
	DeviceTypeOfIOS     DeviceType = "ios"
)

// TaskStatus 任务状态
type TaskStatus string

const (
	TaskStatusOfNull TaskStatus = "" // 空

	TaskStatusOfProcessing TaskStatus = "processing" // 执行中
	TaskStatusOfSucceed    TaskStatus = "succeed"    // 成功
	TaskStatusOfFailed     TaskStatus = "failed"     // 失败
	TaskStatusOfTerminated TaskStatus = "terminate"  // 已终止
)

// StepStatus 步骤状态
type StepStatus string

const (
	StepStatusOfNull StepStatus = "" // 空

	StepStatusOfRunning    StepStatus = "running"   // 运行中
	StepStatusOfCompleted  StepStatus = "completed" // 已完成
	StepStatusOfFailed     StepStatus = "failed"    // 失败
	StepStatusOfTerminated StepStatus = "terminate" // 已终止
)
