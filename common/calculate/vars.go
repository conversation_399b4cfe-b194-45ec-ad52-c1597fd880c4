package calculate

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

var (
	defaultAuthRateLimits = []*pb.RateLimitV2{
		{
			TargetRps:      ConstDefaultAuthTargetRPS,
			InitialRps:     ConstDefaultAuthInitialRPS,
			ChangeDuration: ConstDefaultAuthChangeDuration.String(),
			TargetDuration: "",
		},
	}
	defaultHeartbeatRateLimits = []*pb.RateLimitV2{
		{
			TargetRps:      ConstDefaultHeartbeatTargetRPS,
			InitialRps:     ConstDefaultHeartbeatInitialRPS,
			ChangeDuration: ConstDefaultHeartbeatChangeDuration.String(),
			TargetDuration: "",
		},
	}
)
