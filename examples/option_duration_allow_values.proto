// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/duration.proto";

message StartChessMatchRequest {
  google.protobuf.Duration duration = 1 [(buf.validate.field).duration = {
    // `in` validates that this duration must be a value from the list specified.
    // In this case, it validates that the duration must be 300 seconds, 60 seconds,
    // 120 seconds or 180 seconds.
    in: [
      {seconds: 300},
      {seconds: 60},
      {seconds: 120},
      {seconds: 180}]
  }];
}
