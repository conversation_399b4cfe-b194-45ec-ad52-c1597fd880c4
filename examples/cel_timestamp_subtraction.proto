// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/timestamp.proto";

service ReservationService {
  // BookReservation books an reservation.
  rpc BookReservation(BookReservationRequest) returns (BookReservationResponse);
}

message BookReservationRequest {
  google.protobuf.Timestamp start_time = 1 [(buf.validate.field).cel = {
    id: "book_24_hrs_ahead"
    message: "must book at least 24 hours ahead"
    // `now` evaluates to a timestamp at the current time.
    // Subtracting two timestamps evaluates to a duration. In this case,
    // `this - now` evaluates to the time between now and time of the
    // reservation. We are comparing the time period between now and the
    // reservation to 24 hours and see if right now is early enough for booking
    // the reservation.
    // Note that a duration can be created from `duration(string)`, where the
    // string has the form of `1m6s`. The suffix supported are "h", "m", "s",
    // "ms", "us" and "ns".
    expression: "duration('24h') <= this - now"
  }];
}

message BookReservationResponse {}
