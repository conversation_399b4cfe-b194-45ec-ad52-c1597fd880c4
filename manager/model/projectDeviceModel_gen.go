// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	projectDeviceTableName           = "`project_device`"
	projectDeviceFieldNames          = builder.RawFieldNames(&ProjectDevice{})
	projectDeviceRows                = strings.Join(projectDeviceFieldNames, ",")
	projectDeviceRowsExpectAutoSet   = strings.Join(stringx.Remove(projectDeviceFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	projectDeviceRowsWithPlaceHolder = strings.Join(stringx.Remove(projectDeviceFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerProjectDeviceIdPrefix            = "cache:manager:projectDevice:id:"
	cacheManagerProjectDeviceProjectIdUdidPrefix = "cache:manager:projectDevice:projectId:udid:"
)

type (
	projectDeviceModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ProjectDevice) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ProjectDevice, error)
		FindOneByProjectIdUdid(ctx context.Context, projectId string, udid string) (*ProjectDevice, error)
		Update(ctx context.Context, session sqlx.Session, data *ProjectDevice) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultProjectDeviceModel struct {
		sqlc.CachedConn
		table string
	}

	ProjectDevice struct {
		Id        int64          `db:"id"`         // 自增ID
		ProjectId string         `db:"project_id"` // 项目ID
		Udid      string         `db:"udid"`       // 设备编号
		Usage     int64          `db:"usage"`      // 用途（UI测试、稳定性测试）
		Deleted   int64          `db:"deleted"`    // 逻辑删除标识（未删除、已删除）
		CreatedBy string         `db:"created_by"` // 创建者的用户ID
		UpdatedBy string         `db:"updated_by"` // 最近一次更新者的用户ID
		DeletedBy sql.NullString `db:"deleted_by"` // 删除者的用户ID
		CreatedAt time.Time      `db:"created_at"` // 创建时间
		UpdatedAt time.Time      `db:"updated_at"` // 更新时间
		DeletedAt sql.NullTime   `db:"deleted_at"` // 删除时间
	}
)

func newProjectDeviceModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultProjectDeviceModel {
	return &defaultProjectDeviceModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`project_device`",
	}
}

func (m *defaultProjectDeviceModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerProjectDeviceIdKey := fmt.Sprintf("%s%v", cacheManagerProjectDeviceIdPrefix, id)
	managerProjectDeviceProjectIdUdidKey := fmt.Sprintf("%s%v:%v", cacheManagerProjectDeviceProjectIdUdidPrefix, data.ProjectId, data.Udid)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerProjectDeviceIdKey, managerProjectDeviceProjectIdUdidKey)
	return err
}

func (m *defaultProjectDeviceModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerProjectDeviceIdKey := fmt.Sprintf("%s%v", cacheManagerProjectDeviceIdPrefix, id)
	managerProjectDeviceProjectIdUdidKey := fmt.Sprintf("%s%v:%v", cacheManagerProjectDeviceProjectIdUdidPrefix, data.ProjectId, data.Udid)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerProjectDeviceIdKey, managerProjectDeviceProjectIdUdidKey)
	return err
}

func (m *defaultProjectDeviceModel) FindOne(ctx context.Context, id int64) (*ProjectDevice, error) {
	managerProjectDeviceIdKey := fmt.Sprintf("%s%v", cacheManagerProjectDeviceIdPrefix, id)
	var resp ProjectDevice
	err := m.QueryRowCtx(ctx, &resp, managerProjectDeviceIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", projectDeviceRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultProjectDeviceModel) FindOneByProjectIdUdid(ctx context.Context, projectId string, udid string) (*ProjectDevice, error) {
	managerProjectDeviceProjectIdUdidKey := fmt.Sprintf("%s%v:%v", cacheManagerProjectDeviceProjectIdUdidPrefix, projectId, udid)
	var resp ProjectDevice
	err := m.QueryRowIndexCtx(ctx, &resp, managerProjectDeviceProjectIdUdidKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `udid` = ? and `deleted` = ? limit 1", projectDeviceRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, udid, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultProjectDeviceModel) Insert(ctx context.Context, session sqlx.Session, data *ProjectDevice) (sql.Result, error) {
	managerProjectDeviceIdKey := fmt.Sprintf("%s%v", cacheManagerProjectDeviceIdPrefix, data.Id)
	managerProjectDeviceProjectIdUdidKey := fmt.Sprintf("%s%v:%v", cacheManagerProjectDeviceProjectIdUdidPrefix, data.ProjectId, data.Udid)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?)", m.table, projectDeviceRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.Udid, data.Usage, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.Udid, data.Usage, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerProjectDeviceIdKey, managerProjectDeviceProjectIdUdidKey)
}

func (m *defaultProjectDeviceModel) Update(ctx context.Context, session sqlx.Session, newData *ProjectDevice) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerProjectDeviceIdKey := fmt.Sprintf("%s%v", cacheManagerProjectDeviceIdPrefix, data.Id)
	managerProjectDeviceProjectIdUdidKey := fmt.Sprintf("%s%v:%v", cacheManagerProjectDeviceProjectIdUdidPrefix, data.ProjectId, data.Udid)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, projectDeviceRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.Udid, newData.Usage, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.Udid, newData.Usage, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerProjectDeviceIdKey, managerProjectDeviceProjectIdUdidKey)
}

func (m *defaultProjectDeviceModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerProjectDeviceIdPrefix, primary)
}

func (m *defaultProjectDeviceModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", projectDeviceRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultProjectDeviceModel) tableName() string {
	return m.table
}
