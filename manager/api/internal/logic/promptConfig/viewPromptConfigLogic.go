package promptConfig

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewPromptConfigLogic struct {
	*BaseLogic
}

func NewViewPromptConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewPromptConfigLogic {
	return &ViewPromptConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewPromptConfigLogic) ViewPromptConfig(req *types.ViewPromptConfigReq) (
	resp *types.ViewPromptConfigResp, err error,
) {
	in := &pb.ViewPromptConfigurationReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPromptConfigurationRPC.ViewPromptConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewPromptConfigResp{PromptConfiguration: &types.PromptConfiguration{}}
	if err = utils.Copy(resp.PromptConfiguration, out.GetConfiguration(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
