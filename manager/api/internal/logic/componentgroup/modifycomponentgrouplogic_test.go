package componentgroup

import (
	"testing"

	"github.com/r3labs/diff/v3"
	"github.com/zeromicro/go-zero/core/jsonx"

	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

func TestImportsDiff(t *testing.T) {
	differ, err := diff.NewDiffer()
	if err != nil {
		t.Fatal(err)
	}

	tests := []struct {
		name     string
		original []*types.Import
		current  []*types.Import
	}{
		{
			name: "修改参数值",
			original: []*types.Import{
				{
					Name:   "name",
					Source: 0,
					Manual: &commontypes.VariableValue{
						Value: "allen",
					},
					Export:      nil,
					Environment: nil,
				},
			},
			current: []*types.Import{
				{
					Name:   "name",
					Source: 0,
					Manual: &commontypes.VariableValue{
						Value: "bob",
					},
					Export:      nil,
					Environment: nil,
				},
			},
		},
		{
			name: "删除参数",
			original: []*types.Import{
				{
					Name:   "name",
					Source: 0,
					Manual: &commontypes.VariableValue{
						Value: "allen",
					},
					Export:      nil,
					Environment: nil,
				},
				{
					Name:   "age",
					Source: 1,
					Manual: nil,
					Export: &commontypes.VariableNodeValue{
						NodeId: "node_id:1",
						VariableValue: commontypes.VariableValue{
							Value: "age",
						},
					},
					Environment: nil,
				},
			},
			current: []*types.Import{
				{
					Name:   "name",
					Source: 0,
					Manual: &commontypes.VariableValue{
						Value: "allen",
					},
					Export:      nil,
					Environment: nil,
				},
			},
		},
	}
	for _, tt := range tests {
		cl, err := differ.Diff(tt.original, tt.current)
		if err != nil {
			t.Fatal(err)
		}
		for _, c := range cl {
			t.Logf("%s: %s", tt.name, jsonx.MarshalToStringIgnoreError(c))
		}
	}
}
