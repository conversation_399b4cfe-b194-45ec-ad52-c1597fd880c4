package projectdeviceservicelogic

import (
	"context"
	"fmt"
	"slices"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic/hashmap"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyProjectDeviceLogic struct {
	*BaseLogic

	deleteLogic *DeleteDisabledProjectDeviceLogic
}

func NewModifyProjectDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyProjectDeviceLogic {
	return &ModifyProjectDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),

		deleteLogic: NewDeleteDisabledProjectDeviceLogic(ctx, svcCtx),
	}
}

// ModifyProjectDevice 编辑项目设备列表
func (l *ModifyProjectDeviceLogic) ModifyProjectDevice(in *pb.ModifyProjectDeviceReq) (
	out *pb.ModifyProjectDeviceResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	key := fmt.Sprintf("%s:%s", common.ConstLockProjectDeviceProjectID, in.GetProjectId())
	fn := func() error {
		out, err = l.modify(in)
		return err
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	return out, nil
}

func (l *ModifyProjectDeviceLogic) modify(req *pb.ModifyProjectDeviceReq) (*pb.ModifyProjectDeviceResp, error) {
	var (
		projectID = req.GetProjectId()
		devices   = req.GetDevices()

		capacity = uint64(len(devices))
	)

	allSet, _, err := l.getAllDevices()
	if err != nil {
		return nil, err
	}

	fromSet, fromMap, err := l.getProjectDevices(projectID, commonpb.DeviceUsage_DU_NULL)
	if err != nil {
		return nil, err
	}

	result := &pb.ModifyProjectDeviceResp{
		CreateItems:       make([]*pb.BindDevice, 0, capacity),
		UpdateItems:       make([]*pb.BindDevice, 0, capacity),
		DeleteItems:       make([]*pb.BindDevice, 0, fromSet.Size()),
		IgnoreCreateItems: make([]*pb.BindDevice, 0, capacity),
		IgnoreUpdateItems: make([]*pb.DeviceRelationship, 0, capacity),
		IgnoreDeleteItems: make([]*pb.DeviceRelationship, 0, fromSet.Size()),
	}

	relMap, err := l.getProjectDeviceRelationship(projectID)
	if err != nil {
		return nil, err
	}

	deleteFlag := false
	defer func() {
		if err == nil && deleteFlag {
			_ = l.deleteLogic.DeleteDisabledProjectDeviceForInternal(projectID)
		}
	}()

	err = l.svcCtx.ProjectDeviceModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			deleteFlag, err = l.processToDevices(context, session, projectID, devices, allSet, fromMap, relMap, result)
			if err != nil {
				return err
			}

			deleteFlag, err = l.processFromDevices(
				context, session, projectID, devices, allSet, fromSet, fromMap, relMap, result,
			)
			if err != nil {
				return err
			}

			return nil
		},
	)
	return result, err
}

func (l *ModifyProjectDeviceLogic) processToDevices(
	ctx context.Context,
	session sqlx.Session,
	projectID string,
	devices []*pb.BindDevice,
	allSet *set.Set[string],
	fromMap *hashmap.Map[string, *model.ProjectDevice],
	relMap *hashmap.Map[string, int64],
	result *pb.ModifyProjectDeviceResp,
) (bool, error) {
	var (
		deleteFlag = false
		now        = time.Now()
	)

	for _, device := range devices {
		udid := device.GetUdid()
		usage := device.GetUsage()

		if udid == "" || usage == commonpb.DeviceUsage_DU_NULL {
			continue
		}

		ok1 := allSet.Has(udid)
		fromVal, ok2 := fromMap.Get(udid)
		relVal, ok3 := relMap.Get(udid)

		if !ok1 && !ok2 {
			// `all`没有 且 `from`没有 且 `to`有 => 忽略新增
			result.IgnoreCreateItems = append(
				result.IgnoreCreateItems, &pb.BindDevice{
					Udid:  udid,
					Usage: usage,
				},
			)
			continue
		} else if !ok1 {
			// `all`没有 且 `from`有 且 `to`有 => 忽略更新，内部删除
			deleteFlag = true
		} else if !ok2 {
			// `all`有 且 `from`没有 且 `to`有 => 新增
			if err := l.handleDeviceCreate(
				ctx, session, &model.ProjectDevice{
					ProjectId: projectID,
					Udid:      udid,
					Usage:     int64(usage),
					CreatedBy: l.currentUser.Account,
					UpdatedBy: l.currentUser.Account,
					CreatedAt: now,
					UpdatedAt: now,
				}, result,
			); err != nil {
				return deleteFlag, err
			}
		} else {
			// `all`有 且 `from`有 且 `to`有 => 更新
			if fromVal.Usage != int64(usage) && ok3 && relVal > 0 {
				// 「用途」变更 且 已关联计划 => 忽略更新
				result.IgnoreUpdateItems = append(
					result.IgnoreUpdateItems, &pb.DeviceRelationship{
						Udid:  udid,
						Count: relVal,
					},
				)
				continue
			}

			if err := l.handleDeviceUpdate(
				ctx, session, &model.ProjectDevice{
					Id:        fromVal.Id,
					ProjectId: fromVal.ProjectId,
					Udid:      udid,
					Usage:     int64(usage),
					CreatedBy: fromVal.CreatedBy,
					UpdatedBy: l.currentUser.Account,
					CreatedAt: fromVal.CreatedAt,
					UpdatedAt: now,
				}, result,
			); err != nil {
				return deleteFlag, err
			}
		}
	}

	return deleteFlag, nil
}

func (l *ModifyProjectDeviceLogic) processFromDevices(
	ctx context.Context,
	session sqlx.Session,
	_ string,
	devices []*pb.BindDevice,
	allSet *set.Set[string],
	fromSet *set.Set[string],
	fromMap *hashmap.Map[string, *model.ProjectDevice],
	relMap *hashmap.Map[string, int64],
	result *pb.ModifyProjectDeviceResp,
) (bool, error) {
	deleteFlag := false

	for _, udid := range fromSet.Keys() {
		device, ok := fromMap.Get(udid)
		if !ok {
			continue
		}

		ok1 := allSet.Has(udid)
		ok2 := slices.ContainsFunc(
			devices, func(d *pb.BindDevice) bool {
				return d.GetUdid() == udid
			},
		)
		relVal, ok3 := relMap.Get(udid)

		// 注：下面的情况在上面已经判断过，因此这里就不再判断
		// `all`没有 且 `from`有 且 `to`有 => 忽略更新，内部删除（上面的循环已判断这个场景）
		// `all`有 且 `from`有 且 `to`有 => 更新（上面的循环已判断这个场景）
		if !ok1 && !ok2 {
			// `all`没有 且 `from`有 且 `to`没有 => 内部删除
			deleteFlag = true
		} else if ok1 && !ok2 {
			// `all`有 且 `from`有 且 `to`没有 => 删除
			if ok3 && relVal > 0 {
				// 已关联计划 => 忽略删除
				result.IgnoreDeleteItems = append(
					result.IgnoreDeleteItems, &pb.DeviceRelationship{
						Udid:  udid,
						Count: relVal,
					},
				)
				continue
			}

			if err := l.handleDeviceDelete(ctx, session, device, result); err != nil {
				return deleteFlag, err
			}
		}
	}

	return deleteFlag, nil
}

func (l *ModifyProjectDeviceLogic) handleDeviceCreate(
	ctx context.Context, session sqlx.Session, data *model.ProjectDevice, result *pb.ModifyProjectDeviceResp,
) error {
	if _, err := l.svcCtx.ProjectDeviceModel.Insert(ctx, session, data); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to insert values to table, table: %s, values: %s, error: %+v",
			l.svcCtx.ProjectDeviceModel.Table(), jsonx.MarshalIgnoreError(data), err,
		)
	}

	result.CreateItems = append(
		result.CreateItems, &pb.BindDevice{
			Udid:  data.Udid,
			Usage: commonpb.DeviceUsage(data.Usage),
		},
	)
	return nil
}

func (l *ModifyProjectDeviceLogic) handleDeviceUpdate(
	ctx context.Context, session sqlx.Session, data *model.ProjectDevice, result *pb.ModifyProjectDeviceResp,
) error {
	if _, err := l.svcCtx.ProjectDeviceModel.Update(ctx, session, data); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update values to table, table: %s, values: %s, error: %+v",
			l.svcCtx.ProjectDeviceModel.Table(), jsonx.MarshalIgnoreError(data), err,
		)
	}

	result.UpdateItems = append(
		result.UpdateItems, &pb.BindDevice{
			Udid:  data.Udid,
			Usage: commonpb.DeviceUsage(data.Usage),
		},
	)
	return nil
}

func (l *ModifyProjectDeviceLogic) handleDeviceDelete(
	ctx context.Context, session sqlx.Session, data *model.ProjectDevice, result *pb.ModifyProjectDeviceResp,
) error {
	if err := l.svcCtx.ProjectDeviceModel.Delete(ctx, session, data.Id); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to delete item from table, table: %s, item: %s, error: %+v",
			l.svcCtx.ProjectDeviceModel.Table(), jsonx.MarshalIgnoreError(data), err,
		)
	}

	result.DeleteItems = append(
		result.DeleteItems, &pb.BindDevice{
			Udid:  data.Udid,
			Usage: commonpb.DeviceUsage(data.Usage),
		},
	)
	return nil
}
