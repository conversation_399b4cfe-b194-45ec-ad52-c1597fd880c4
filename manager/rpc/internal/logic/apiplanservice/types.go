package apiplanservicelogic

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

type createAccountConfigReferenceInternalReq struct {
	ProjectId     string               `json:"project_id"`
	ReferenceType common.ReferenceType `json:"reference_type"`
	ReferenceId   string               `json:"reference_id"`
	ConfigIds     []string             `json:"config_ids"`
}

type CreateOrRemoveReferenceInternalReq struct {
	ProjectId         string               `json:"project_id"`
	ReferenceParentId string               `json:"reference_parent_id"`
	ReferenceType     common.ReferenceType `json:"reference_type"`
	ReferenceIds      []string             `json:"reference_ids"`
	PlanId            string               `json:"plan_id"`
	NoNeedToCheck     bool                 `json:"no_need_to_check"`
}

type CreateOrRemoveReference struct {
	ReferenceParentId string               `json:"reference_parent_id"`
	ReferenceType     common.ReferenceType `json:"reference_type"`
	ReferenceId       string               `json:"reference_id"`
}

type CreateOrRemoveReference2InternalReq struct {
	ProjectId     string                     `json:"project_id"`
	References    []*CreateOrRemoveReference `json:"references"`
	PlanId        string                     `json:"plan_id"`
	NoNeedToCheck bool                       `json:"no_need_to_check"`
}
