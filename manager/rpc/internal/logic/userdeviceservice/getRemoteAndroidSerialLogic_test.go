package userdeviceservicelogic

import (
	"context"
	"os/exec"
	"reflect"
	"syscall"
	"testing"

	"github.com/zeromicro/go-zero/core/logx"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func TestGetRemoteAndroidSerialLogic_GetRemoteAndroidSerial(t *testing.T) {
	ctx := context.Background()
	l := &GetRemoteAndroidSerialLogic{
		ctx:    ctx,
		svcCtx: &svc.ServiceContext{}, // 当前测试不需要使用`svcCtx`
		Logger: logx.WithContext(ctx),
	}

	type args struct {
		in *pb.GetRemoteAndroidSerialReq
	}
	tests := []struct {
		name    string
		args    args
		wantOut *pb.GetRemoteAndroidSerialResp
		wantErr bool
	}{
		{
			name: "real phone",
			args: args{
				in: &pb.GetRemoteAndroidSerialReq{
					DeviceType:    commonpb.DeviceType_REAL_PHONE,
					RemoteAddress: "************:20004",
				},
			},
			wantOut: &pb.GetRemoteAndroidSerialResp{Serial: "LZYTYLZT9HFI6DLN"},
			wantErr: false,
		},
		{
			name: "cloud phone",
			args: args{
				in: &pb.GetRemoteAndroidSerialReq{
					DeviceType:    commonpb.DeviceType_CLOUD_PHONE,
					RemoteAddress: "127.0.0.1:12345",
				},
			},
			wantOut: &pb.GetRemoteAndroidSerialResp{Serial: "c589414904b44398bce59f86b2e9de29"},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if tt.args.in.GetDeviceType() == commonpb.DeviceType_CLOUD_PHONE {
					// 对于云手机，需要创建SSH隧道
					cmd := exec.CommandContext(
						ctx, "ssh", "-L", "12345:***********:5555", "08b242bf6b80f5d52f98c00a6a6139d2@**************",
						"-i", "/Users/<USER>/cph-TT-game.pem", "-o", "ServerAliveInterval=30", "-Nf",
					)
					cmd.SysProcAttr = &syscall.SysProcAttr{Setpgid: true}
					if err := cmd.Start(); err != nil {
						t.Fatal(err)
					}
					defer func() {
						if err := cmd.Process.Kill(); err != nil {
							t.Fatal(err)
						}
					}()
				}

				gotOut, err := l.GetRemoteAndroidSerial(tt.args.in)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetRemoteAndroidSerial() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(gotOut, tt.wantOut) {
					t.Errorf("GetRemoteAndroidSerial() gotOut = %v, want %v", gotOut, tt.wantOut)
				}
			},
		)
	}
}
