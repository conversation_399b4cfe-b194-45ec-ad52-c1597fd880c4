package larkchatservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UpdateLarkChatLogic struct {
	*BaseLogic
}

func NewUpdateLarkChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLarkChatLogic {
	return &UpdateLarkChatLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// UpdateLarkChat 更新测试通知飞书群组（由飞书群配置修改事件触发）
func (l *UpdateLarkChatLogic) UpdateLarkChat(in *pb.UpdateLarkChatReq) (out *pb.UpdateLarkChatResp, err error) {
	key := fmt.Sprintf("%s:%s", common.ConstLockLarkChatChatIDPrefix, in.GetChatId())
	fn := func() error {
		req := model.UpdateLarkChatReq{
			ChatID:      in.GetChatId(),
			Name:        in.GetName(),
			Avatar:      in.GetAvatar(),
			Description: in.GetDescription(),
			External:    in.GetExternal(),
		}
		if _, err := l.svcCtx.LarkChatModel.UpdateByChatID(l.ctx, nil, req); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to update values to table, table: %s, values: %s, error: %+v",
				l.svcCtx.LarkChatModel.Table(), jsonx.MarshalIgnoreError(req), err,
			)
		}

		return nil
	}
	if err = caller.LockWithOptionDo(
		l.svcCtx.Redis, key, fn,
		redislock.WithTimeout(common.ConstExpireOfLarkChatUpdatedTask),
	); err != nil {
		return nil, err
	}

	return &pb.UpdateLarkChatResp{}, nil
}
