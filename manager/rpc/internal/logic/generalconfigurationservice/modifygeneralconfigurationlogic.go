package generalconfigurationservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyGeneralConfigurationLogic struct {
	*BaseLogic
}

func NewModifyGeneralConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyGeneralConfigurationLogic {
	return &ModifyGeneralConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyGeneralConfiguration 编辑通用配置
func (l *ModifyGeneralConfigurationLogic) ModifyGeneralConfiguration(in *pb.ModifyGeneralConfigurationReq) (
	resp *pb.ModifyGeneralConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the config_id in req
	origin, err := model.CheckGeneralConfigByConfigId(
		l.ctx, l.svcCtx.GeneralConfigModel, in.GetProjectId(), in.GetConfigId(),
	)
	if err != nil {
		return nil, err
	}

	if in.GetVariables() == nil {
		in.SetVariables(make([]*commonpb.GeneralConfigVar, 0))
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockGeneralConfigProjectIdConfigIdPrefix, in.GetProjectId(), in.GetConfigId(),
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	configuration := &model.GeneralConfiguration{
		Id:        origin.Id,
		ProjectId: origin.ProjectId,
		ConfigId:  origin.ConfigId,
		Type:      origin.Type,
		Name:      in.GetName(),
		Description: sql.NullString{
			String: in.GetDescription(),
			Valid:  in.GetDescription() != "",
		},
		BaseUrl: sql.NullString{
			String: in.GetBaseUrl(),
			Valid:  in.GetBaseUrl() != "",
		},
		Verify: func() int64 {
			if in.GetVerify() {
				return 1
			}
			return 0
		}(),
		Variables: protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetVariables()),
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
		UpdatedAt: time.Now(),
	}

	if _, err = l.svcCtx.GeneralConfigModel.Update(l.ctx, nil, configuration); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update table[%s] with values[%+v], error: %+v",
			l.svcCtx.GeneralConfigModel.Table(), origin, err,
		)
	}

	resp = &pb.ModifyGeneralConfigurationResp{Configuration: &pb.GeneralConfiguration{}}
	if err = utils.Copy(resp.Configuration, configuration, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy general configuration[%+v] to response, error: %+v",
			configuration, err,
		)
	}

	return resp, nil
}
