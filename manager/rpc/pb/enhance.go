// 对于ProtoBuf生成的结构体，可以在此文件编写增强方法

package pb

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

type componentExecutionDataFunc func() (isApiExecutionData_Data, protobuf.ValidateMessage)

type FunctionComponent interface {
	Functions() []common.Function
}

func (x *VariableFunction) getFunction() common.Function {
	return common.Function{
		Name: x.GetName(),
		Type: x.GetType().String(),
	}
}

func getFunctionsFromImports(imports []*Import) (out []common.Function) {
	out = make([]common.Function, 0, len(imports))

	for _, i := range imports {
		if i == nil || i.Function == nil || i.Function.Name == "" {
			continue
		}

		out = append(out, i.Function.getFunction())
	}

	return out
}

func (x *SetupComponent) Functions() []common.Function {
	return getFunctionsFromImports(x.GetImports())
}

func (x *TeardownComponent) Functions() []common.Function {
	return getFunctionsFromImports(x.GetImports())
}

func (x *BusinessSingleComponent) Functions() []common.Function {
	return getFunctionsFromImports(x.GetImports())
}

func (x *BusinessGroupComponent) Functions() []common.Function {
	return getFunctionsFromImports(x.GetImports())
}

func (x *LoopComponent_While_SingleCondition) getFunctions() (out []common.Function) {
	out = make([]common.Function, 0, 2)

	var (
		leftFunction  = x.GetLeft().GetFunction()
		rightFunction = x.GetRight().GetFunction()
	)

	if leftFunction != nil {
		out = append(out, leftFunction.getFunction())
	}
	if rightFunction != nil {
		out = append(out, rightFunction.getFunction())
	}

	return out
}

func (x *LoopComponent_While) getFunctions() (out []common.Function) {
	var (
		typ    = x.GetType()
		single = x.GetSingle()
		group  = x.GetGroup()
	)

	if typ == ConditionType_SINGLE && single != nil {
		out = append(out, single.getFunctions()...)
	} else if typ == ConditionType_GROUP && group != nil {
		for _, w := range group.GetConditions() {
			out = append(out, w.getFunctions()...)
		}
	}

	return out
}

func (x *LoopComponent) Functions() (out []common.Function) {
	var (
		function = x.GetForEach().GetFunction()
		while    = x.GetWhile()
	)

	if function != nil {
		out = append(out, function.getFunction())
	}

	if while != nil {
		out = append(out, while.getFunctions()...)
	}

	return out
}

func (x *HttpRequestComponent) Functions() []common.Function {
	return getFunctionsFromImports(x.GetImports())
}

func (x *ReferenceComponent) Functions() []common.Function {
	return getFunctionsFromImports(x.GetImports())
}

func (x *ConditionComponent_SingleCondition) getFunctions() (out []common.Function) {
	var (
		leftFunction  = x.GetLeft().GetFunction()
		rightFunction = x.GetRight().GetFunction()
	)

	if leftFunction != nil {
		out = append(out, leftFunction.getFunction())
	}
	if rightFunction != nil {
		out = append(out, rightFunction.getFunction())
	}

	return out
}

func (x *ConditionComponent) Functions() (out []common.Function) {
	var (
		typ    = x.GetType()
		single = x.GetSingle()
		group  = x.GetGroup()
	)

	if typ == ConditionType_SINGLE && single != nil {
		out = append(out, single.getFunctions()...)
	} else if typ == ConditionType_GROUP && group != nil {
		for _, c := range group.GetConditions() {
			out = append(out, c.Functions()...)
		}
	}

	return out
}

func (x *AssertComponent) Functions() (out []common.Function) {
	for _, a := range x.GetAssertions() {
		if f := a.GetExpected().GetFunction(); f != nil {
			out = append(out, f.getFunction())
		}
	}

	return out
}

func (x *DataProcessingComponent) Functions() (out []common.Function) {
	for _, p := range x.GetProcesses() {
		if f := p.GetFunction(); f != nil {
			out = append(out, f.getFunction())
		}
	}

	return out
}

func (x *SuiteTypeId) Key() string {
	return x.GetSuiteType() + ":" + x.GetSuiteId()
}

func (x *CreateCategoryReq) SetIndex(index int64) {
	if x != nil {
		x.Index = index
	}
}

func (x *GetCategoryTreeReq) SetCategoryId(categoryId string) {
	if x != nil {
		x.CategoryId = categoryId
	}
}

func (x *CreateGeneralConfigurationReq) SetVariables(variables []*pb.GeneralConfigVar) {
	if x != nil {
		x.Variables = variables
	}
}

func (x *ModifyGeneralConfigurationReq) SetVariables(variables []*pb.GeneralConfigVar) {
	if x != nil {
		x.Variables = variables
	}
}

func (x *ModifyInterfaceConfigReq) SetInputParameters(inputParameters []*InputParameter) {
	if x != nil {
		x.InputParameters = inputParameters
	}
}

func (x *ModifyInterfaceConfigReq) SetOutputParameters(outputParameters []*OutputParameter) {
	if x != nil {
		x.OutputParameters = outputParameters
	}
}

func (x *Statistic) ConvertTo() *common.Statistic {
	return common.NewStatistic(
		x.GetTotal(), x.GetIncreased(), x.GetModified(), x.GetUnchanged(), x.GetSkipped(), x.GetFailure(),
	)
}

func (x ResourceState) ConvertTo() common.ResourceState {
	switch x {
	case ResourceState_RS_ENABLED:
		return common.ConstResourceStateEnabled
	case ResourceState_RS_DISABLED:
		return common.ConstResourceStateDisabled
	case ResourceState_RS_NEW:
		return common.ConstResourceStateNew
	case ResourceState_RS_TO_BE_IMPLEMENTED:
		return common.ConstResourceStateToBeImplemented
	case ResourceState_RS_TO_BE_MAINTAINED:
		return common.ConstResourceStateToBeMaintained
	case ResourceState_RS_PENDING_REVIEW:
		return common.ConstResourceStatePendingReview
	case ResourceState_RS_PUBLISHED:
		return common.ConstResourceStatePublished
	default:
		return ""
	}
}

func (x ReviewStatus) ConvertTo() common.ReviewStatus {
	switch x {
	case ReviewStatus_REVIEW_STATUS_PENDING:
		return common.ConstReviewStatusPending
	case ReviewStatus_REVIEW_STATUS_REVOKED:
		return common.ConstReviewStatusRevoked
	case ReviewStatus_REVIEW_STATUS_APPROVED:
		return common.ConstReviewStatusApproved
	case ReviewStatus_REVIEW_STATUS_REJECTED:
		return common.ConstReviewStatusRejected
	default:
		return ""
	}
}

func (x ReviewResourceType) ConvertTo() common.ReviewResourceType {
	switch x {
	case ReviewResourceType_RRT_COMPONENT_GROUP:
		return common.ConstReviewResourceTypeComponentGroup
	case ReviewResourceType_RRT_CASE:
		return common.ConstReviewResourceTypeCase
	case ReviewResourceType_RRT_SETUP_COMPONENT:
		return common.ConstReviewResourceTypeSetupComponent
	case ReviewResourceType_RRT_TEARDOWN_COMPONENT:
		return common.ConstReviewResourceTypeTeardownComponent
	case ReviewResourceType_RRT_BUSINESS_COMPONENT:
		return common.ConstReviewResourceTypeBusinessComponent
	case ReviewResourceType_RRT_API_CASE:
		return common.ConstReviewResourceTypeAPICase
	case ReviewResourceType_RRT_INTERFACE_CASE:
		return common.ConstReviewResourceTypeInterfaceCase
	default:
		return ""
	}
}
