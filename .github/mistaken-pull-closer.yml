# The JSONPath filter expression used to identify which PRs to close.
# The data filtered is the pull request data along with other metadata passed in
# by probot.
# Default behavior: Close all PRs.
filters:
  # Don't close PRs in the pubsub_dev branch.
  - '@.pull_request.base.ref != "pubsub_dev"'
  - '@.pull_request.base.ref != "diregapic"'

# The message to post to the closed PR.
commentBody: |
  Thanks for your contribution!  Unfortunately, we don't use GitHub pull
  requests to manage code contributions to this repository.  Instead, please
  see [CONTRIBUTING.md](../blob/master/CONTRIBUTING.md) which provides full
  instructions on how to get involved.

# Whether to add a label to the closed PR.
addLabel: false
