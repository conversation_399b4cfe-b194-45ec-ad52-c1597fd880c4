package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ AlertModel = (*customAlertModel)(nil)

	alertInsertFields = stringx.Remove(
		alertFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)
)

type (
	// AlertModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAlertModel.
	AlertModel interface {
		alertModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *Alert) squirrel.InsertBuilder
		UpdateBuilder(data *Alert) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*Alert, error)

		FindAll(ctx context.Context) ([]*Alert, error)
	}

	customAlertModel struct {
		*defaultAlertModel

		conn sqlx.SqlConn
	}
)

// NewAlertModel returns a model for the database table.
func NewAlertModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) AlertModel {
	return &customAlertModel{
		defaultAlertModel: newAlertModel(conn, c, opts...),
		conn:              conn,
	}
}

func (m *customAlertModel) Table() string {
	return m.table
}

func (m *customAlertModel) Fields() []string {
	return alertFieldNames
}

func (m *customAlertModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customAlertModel) InsertBuilder(data *Alert) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(alertInsertFields...).Values(
		data.AlertId, data.RuleId, data.Udid, data.Times, data.StartedAt, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customAlertModel) UpdateBuilder(data *Alert) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`times`":      data.Times,
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customAlertModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(alertFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customAlertModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customAlertModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customAlertModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
	[]*Alert, error,
) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Alert
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customAlertModel) FindAll(ctx context.Context) ([]*Alert, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder())
}
