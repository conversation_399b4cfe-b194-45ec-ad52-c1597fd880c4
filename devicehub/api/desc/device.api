syntax = "v1"

import "device_types.api"

@server (
    prefix: devicehub/v1
    group: device
    timeout: 8s
)
service devicehub {
    @doc "search devices"
    @handler searchDevice
    post /device/search (SearchDeviceReq) returns (SearchDeviceResp)

    @doc "acquire device"
    @handler acquireDevice
    post /device/acquire (AcquireDeviceReq) returns (AcquireDeviceResp)

    @doc "release device"
    @handler releaseDevice
    post /device/release (ReleaseDeviceReq) returns (ReleaseDeviceResp)

    @doc "search acquire device"
    @handler searchAcquireDevice
    post /device/search_acquire (SearchAcquireDeviceReq) returns (SearchAcquireDeviceResp)
}
