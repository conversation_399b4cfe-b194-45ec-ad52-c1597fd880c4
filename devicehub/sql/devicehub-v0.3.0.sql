-- 设备状态监控告警
USE `devicehub`;

ALTER TABLE `device` ADD `previous_state` VARCHAR(16) NULL COMMENT '前一个状态（空闲中、使用中、释放中、已下线、已预留）' AFTER `remote_address`;


CREATE TABLE IF NOT EXISTS `rule`
(
    `id`                INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `rule_id`           VARCHAR(64)  NOT NULL COMMENT '规则ID',
    `name`              VARCHAR(255) NOT NULL COMMENT '规则名称',
    `source_state`      VARCHAR(16)  NOT NULL DEFAULT 'IDLE' COMMENT '原状态（空闲中、使用中、释放中、已下线、已预留）',
    `destination_state` VARCHAR(16)  NOT NULL DEFAULT 'IDLE' COMMENT '目标状态（空闲中、使用中、释放中、已下线、已预留）',
    `duration`          INT          NOT NULL DEFAULT 300 COMMENT '持续时间（单位：秒）',
    `times`             INT          NOT NULL DEFAULT 1 COMMENT '告警次数',
    `deleted`           TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`        VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`        VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`        VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`        TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`        TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`        TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_rule_rule_id` (`rule_id`),
    KEY `ix_rule_rule_id_deleted` (`rule_id`, `deleted`),
    KEY `ix_rule_source_deleted` (`source_state`, `deleted`),
    KEY `ix_rule_destination_deleted` (`destination_state`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT ='规则表';


CREATE TABLE IF NOT EXISTS `alert`
(
    `id`             INT         NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `alert_id`       VARCHAR(64) NOT NULL COMMENT '告警ID',
    `rule_id`        VARCHAR(64) NOT NULL COMMENT '规则ID',
    `udid`           VARCHAR(64) NOT NULL COMMENT '设备编号',
    `times`          INT         NOT NULL DEFAULT 1 COMMENT '已告警的次数',
    `started_at`     TIMESTAMP   NOT NULL COMMENT '开始时间',
    `deleted`        TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_alert_alert_id` (`alert_id`),
    KEY `ix_alert_rule_id_udid_deleted` (`rule_id`, `udid`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT ='告警表';


INSERT INTO
    `rule` (`rule_id`, `name`, `source_state`, `destination_state`, `duration`, `times`, `created_by`, `updated_by`)
VALUES
    ('rule_id:dl5CRti120DiJ6IwixwmP', '设备状态从「使用中」变为「释放中」后持续300秒以上告警1次', 'IN_USE', 'RELEASING', 300, 1, 'probe-system', 'probe-system'),
    ('rule_id:iMSUYzggxD69tO8RyBOsH', '设备状态从「空闲中」变为「已下线」后持续300秒以上告警1次', 'IDLE', 'OFFLINE', 300, 1, 'probe-system', 'probe-system'),
    ('rule_id:90oMGjZHZnpz1-6hcw_XG', '设备状态从「使用中」变为「已下线」后持续300秒以上告警1次', 'IN_USE', 'OFFLINE', 300, 1, 'probe-system', 'probe-system'),
    ('rule_id:-nxyeaq8qpQ1Uea_omCO4', '设备状态从「释放中」变为「已下线」后持续300秒以上告警1次', 'RELEASING', 'OFFLINE', 300, 1, 'probe-system', 'probe-system')
